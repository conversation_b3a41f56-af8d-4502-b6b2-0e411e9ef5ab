import { defineStore } from 'pinia'
import { useLoginStore } from './login'
import { Message } from 'view-ui-plus'
import { useSearchStore } from '@/stores/search'

export const useFieldStore = defineStore('fieldStore', {
  state: () => ({
    fieldPathList: [],
    nowCheckFieldPath: ''
  }),
  actions: {
		getAllPath (){
			const searchStore = useSearchStore()
			searchStore.getPathTreeData()
		},

    // 获取字段
    getFieldPathList(path) {
      const { $data } = useLoginStore().wsMethod
      this.nowCheckFieldPath = path
      $data.sendData(
        'Api.Search.SearchPrefix.ListDir',
        [
          {
            head: {
              row_key: [],
              size: 200
            },
            msg: {
              path: path
            }
          }
        ],
        (res) => {
          this.fieldPathList = res
        }
      )
    },
    // 添加字段
    sendAddFieldPath(addFrom) {
      const { cnName, name, describe, isLock } = addFrom
      const { $data } = useLoginStore().wsMethod
      const that = this
      $data.sendData(
        'Api.Search.SearchPrefix.AddDir',
        [
          {
            head: {},
            msg: {
              path: this.nowCheckFieldPath + '/' + name
            }
          }
        ],
        (res) => {
          if (res.status === 'ok') {
            that.sendEditFirldPath({
              path: this.nowCheckFieldPath + '/' + name,
              map: {
                alias: cnName,
                summary: describe,
                lock: String(isLock)
              },
              tip: '添加字段'
            })
						this.getAllPath()
          } else {
            Message.error({
              background: true,
              content: '添加字段失败'
            })
          }
        }
      )
    },
    // 修改字段
    sendEditFirldPath(editFrom) {
      const { path, map, tip } = editFrom
      const { $data } = useLoginStore().wsMethod
      const that = this
      $data.sendData(
        'Api.Search.SearchPrefix.SetDir',
        [
          {
            head: {},
            msg: {
              path: path,
              map: map
            }
          }
        ],
        (res) => {
          if (res.status === 'ok') {
            Message.success({
              background: true,
              content: tip + '成功'
            })
            that.getFieldPathList(that.nowCheckFieldPath)
          } else {
            Message.error({
              background: true,
              content: tip + '失败'
            })
            if (tip === '添加字段') {
              that.sendDelFieldPath(path)
            }
          }
        }
      )
    },
    // 删除字段
    sendDelFieldPath(path) {
      const { $data } = useLoginStore().wsMethod
      const that = this
      $data.sendData(
        'Api.Search.SearchPrefix.DelDir',
        [
          {
            head: {},
            msg: {
              path: path
            }
          }
        ],
        (res) => {
          if (res.status === 'ok') {
            Message.success({
              background: true,
              content: '删除字段成功'
            })
            that.getFieldPathList(that.nowCheckFieldPath)
          } else {
            Message.error({
              background: true,
              content: '删除字段失败'
            })
          }
        }
      )
    }
  }
})
