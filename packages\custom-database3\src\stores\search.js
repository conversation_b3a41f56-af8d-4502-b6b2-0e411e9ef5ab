import VueCookies from 'vue-cookies'
import { defineStore } from 'pinia'
import { useLoginStore } from './login'
import { Message } from 'view-ui-plus'
import tools from '@/utils/tools'

export const useSearchStore = defineStore('searchStore', {
  state: () => ({
    searchValue: '',
    selectPathValue: [],
    selectPathData: [],
    allPathTreeData: [],
    allPathArr: [],
    caseTreeTimer: null,
    pathTreeTimer: null,
    selectCaseData: [],
    // 搜索所需数据
    searchData: {
      value: '',
      database: '',
      caseId: '',
      pathArr: [],
      prefixRow: [],
      dataRow: [],
      relationStr: ''
    },

    
    combinedSearchList: [], // 新增，合并后的结果列表
    isSearchCombined: false, // 新增，标记所有路径是否都已搜索完毕

    // 前缀搜索类型
    prefixSearchPathIndex: 0,
    prefixSearchList: [],
    // 精确搜索类型
    preciseSearchList: [],
    // 前缀搜索数据
    dataSearchPathIndex: 0,
    dataSearchList: [],
    dataSizeAll: 20,
    dataHaveLastObj: {},
    caseTreeLoad: true,
    pathTreeLoad: true,
    searchPrefixLoad: false,
    searchLastLoad: false,
    searchTimer: null,
    allPathDesc: {},
    prefixSeachOver: false,
    dataSearchOver: false,
    pathCountNum: {},
    pathNumObj: {
      countRes: {
        num: 0,
        timestamp: null,
        indexNum: 0
      },
      countSta: {
        num: 0,
        indexNum:0,
        timestamp: null
      }
    }
  }),
  actions: {
    calculateDataBse(type) {
      const { userinfo } = useLoginStore()
      switch (type) {
        case 'public':
          return type.charAt(0)
        case 'authority':
          return type.charAt(0) + ';' + userinfo.authority
        case 'username':
          return type.charAt(0) + ';' + userinfo.authority + ';' + userinfo.username
        case 'case':
          return type.charAt(0) + ';' + this.searchData.caseId
        default:
          return
      }
    },
    // 获取路径树
    getPathTreeData() {
      let index = 0
      const { $data } = useLoginStore().wsMethod
      const getPathTree = (treeData, path) => {
        index++
        $data.sendData(
          'Api.Search.SearchPrefix.ListDir',
          [
            {
              head: {
                session_id: '',
                row_key: [],
                size: 200
              },
              msg: {
                path: path
              }
            }
          ],
          (res) => {
            res.forEach((item) => {
              let treeObject = {
                title: item.columnValues?.p?.alias
                  ? item.columnValues.p.alias
                  : item.columnValues.i.name,
                expand: true,
                value: item.columnValues.i.path,
                selected: false,
                checked: false,
                children: []
              }
              this.allPathDesc[item.columnValues.i.path] = item.columnValues?.p?.alias
                ? item.columnValues.p.alias
                : item.columnValues.i.path
              this.allPathArr.push(item.columnValues.i.path)
              if (treeData?.children) {
                treeData.children.push(treeObject)
              } else {
                treeData.push(treeObject)
              }
              getPathTree(treeObject, item.columnValues.i.path)
            })
            index--
          },
          () => {
            index--
          }
        )
      }
      getPathTree(this.selectPathData, '/')
      this.pathTreeTimer = setInterval(() => {
        if (index <= 0) {
          clearInterval(this.pathTreeTimer)
          this.allPathTreeData = this.selectPathData
          this.pathTreeLoad = false
          this.allPathArr = [...new Set(this.allPathArr)]
          window.localStorage.setItem('selectPathData', JSON.stringify(this.selectPathData))
          window.localStorage.setItem('allPathArr', JSON.stringify(this.allPathArr))
          window.localStorage.setItem('allPathDesc', JSON.stringify(this.allPathDesc))
        }
      }, 1000)
    },
    clearPathTreeData() {
      this.selectPathData = []
      this.allPathTreeData = []
      this.allPathArr = []
      this.allPathDesc = {}
    },
    // 获取案件树
    getCaseTreeData() {
      let index = 0
      const { $pki, $case } = useLoginStore().wsMethod
      const loginStore = useLoginStore()
      index++
      // 新增定时器轮询获取authority
      const authorityTimer = setInterval(() => {
        let authority = VueCookies.get('authority')
        if (!authority) {
          // 未获取到authority时，调用getUserInfo
          loginStore.getUserInfo()
        } else {
          clearInterval(authorityTimer)
          $pki.sendData(
            'Api.SubAuthority.List',
            [
              {
                head: {
                  from: 0,
                  size: 1000
                },
                msg: {
                  authority: authority,
                  sub_authority_father: '/63617365'
                }
              }
            ],
            (res) => {
              index--
              getCaseTree(this.selectCaseData, res[0].sub_authority)
            }
          )
          const getCaseTree = (caseTree, casePath) => {
            index++
            $case.sendData(
              'Api.CaseDir.List',
              [
                {
                  head: {
                    session_id: '',
                    from: 0,
                    size: 1000
                  },
                  msg: {
                    case_dir_father_path: casePath
                  }
                }
              ],
              (res) => {
                index--
                res.forEach((item) => {
                  const caseDirObj = {
                    value: item.row,
                    label: item.columnValues.i.case_dir_name,
                    disabled: true,
                    children: []
                  }
                  if (caseTree?.children) {
                    caseTree.children.push(caseDirObj)
                    caseTree.disabled = false
                  } else {
                    caseTree.push(caseDirObj)
                  }
                  const dirPath =
                    item.columnValues.i.case_dir_father_path + '/' + item.columnValues.i.case_dir_name
                  getCaseTree(caseDirObj, dirPath)
                })
              },
              () => {
                index--
              }
            )
            index++
            $case.sendData(
              'Api.Case.List',
              [
                {
                  head: {
                    session_id: '',
                    from: 0,
                    size: 1000
                  },
                  msg: {
                    case_dir_father_path: casePath
                  }
                }
              ],
              (res) => {
                index--
                res.forEach((item) => {
                  const caseDirObj = {
                    value: item.row,
                    label: item.columnValues.i.case_name
                  }
                  if (caseTree?.children) {
                    caseTree.children.push(caseDirObj)
                  } else {
                    caseTree.push(caseDirObj)
                  }
                })
              },
              () => {
                index--
              }
            )
          }
          this.caseTreeTimer = setInterval(() => {
            if (index <= 0) {
              clearInterval(this.caseTreeTimer)
              this.caseTreeLoad = false
              localStorage.setItem('selectCaseData', JSON.stringify(this.selectCaseData))
            }
          }, 1000)
        }
      }, 500)
    },
    clearCaseTreeData() {
      this.selectCaseData = []
    },
    // 设置前缀搜索数据类型需要的值
    setSearchData(data) {
      this.dataSearchList = []
      this.dataHaveLastObj = {}
      this.prefixSearchPathIndex = 0
      this.dataSearchPathIndex = 0
      this.dataSizeAll = 20
      this.searchData.value = data.value
      this.searchData.database = data.database
      this.searchData.caseId = data.caseId
      this.searchData.prefixRow = []
      this.searchData.dataRow = []
      
      this.combinedSearchList = []
      this.isSearchComplete = false

      if (data.relationArr.length === 1) {
        this.searchData.relationStr = ''
      } else {
        this.searchData.relationStr = data.relationArr.slice(1).join(';')
      }
      if (data.pathArr.length) {
        this.searchData.pathArr = data.pathArr
      } else {
        this.searchData.pathArr = this.allPathArr
      }
    },

    /** 新增：主控搜索函数 */
    async startInitialSearch() {
      if (this.searchPrefixLoad) return
      this.searchPrefixLoad = true

      const { $data } = useLoginStore().wsMethod
      const { value, database, caseId, pathArr, prefixRow, relationStr } = this.searchData

      let temPath = []
      
      // 1.精确搜索阶段
      const preciseRowKeys = pathArr.map(path => {
        temPath.push({
          row_key: tools.md5(this.calculateDataBse(database) + ';' +  path + (relationStr ? ';' + relationStr : '')) + ';' +  this.calculateDataBse(database) + (relationStr ? ';' + relationStr : '') + ';' + value,
          type: 'public',
          path: path,
          relation: relationStr,
          prefix: value
        })
        return tools.md5(this.calculateDataBse(database) + ';' + 
        path + (relationStr ? ';' + relationStr : '')) + ';' + 
        this.calculateDataBse(database) + (relationStr ? ';' + relationStr : '') + ';' + value;
      })

      const currentPath = pathArr[this.prefixSearchPathIndex];

      const preciseResults = await new Promise((resolve) => {
        $data.sendData(
          'Api.Search.SearchPrefixComp.DetailMultiObj',
          [
            { 
              msg: {
                row_key_obj: temPath
              }
            }
          ],
          (res) => {
            console.log("res:", res);
            
            const results = [];
            Object.keys(res).forEach(key => {
              if (res[key].relation) {
                const path = pathArr.find(p => key.includes(tools.md5(this.calculateDataBse(database) + ';' + p)));
                results.push({ row: key, columnValues: { title: value, path: path || '', r: res[key].relation } });
              }
            });
            resolve(results);
          },
          () => resolve([])
        );
      })

      this.combinedSearchList.push(...preciseResults)

      // 2.逻辑分支判断
      if (preciseResults.length >= 20) {
        this.searchPrefixLoad = false
        return
      }

      // 分支B： 结果未满，自动进入前缀搜索阶段进行补充
      this.searchPrefixLoad = false; // 重置加载状态，让fetchMorePrefixResults可以执行
      this.fetchMorePrefixResults();
    },

    /** 新增： 前缀搜索阶段，用户补充数据和滚动加载 */
    async fetchMorePrefixResults() {
      // 1. 检查守卫条件
      if (this.searchPrefixLoad || this.isSearchComplete) {
        return;
      }
      
      // 如果已经有20条以上的数据，说明当前页已满，等待下一次滚动
      // 这是为了防止在初始加载后自动触发的补充搜索中，如果已经够了20条，就不再继续
      if(this.combinedSearchList.length >= 20 && this.searchData.prefixRow.length > 0) {
          // 但要确保不是因为刚搜完一个path而触发的
      }

      this.searchPrefixLoad = true;
      const { $data } = useLoginStore().wsMethod;
      const { value, database, caseId, pathArr, prefixRow, relationStr } = this.searchData;

      // 2. 检查是否所有路径都已完成
      if (this.prefixSearchPathIndex >= pathArr.length) {
        this.isSearchComplete = true;
        this.searchPrefixLoad = false;
        // 可以在这里添加一个最终的提示
        if (!this.combinedSearchList.length) {
            Message.info({ background: true, content: '未查询到任何结果。' });
        } else {
            // Message.success({ background: true, content: '已加载所有结果分类！' });
        }
        return;
      }

      const currentPath = pathArr[this.prefixSearchPathIndex];
      
      // 3. 执行单次前缀搜索
      const prefixResult = await new Promise((resolve) => {
        $data.sendData(
          'Api.Search.SearchPrefixComp.Query',
          [{ head: { row_key: prefixRow, size: 20, family: ['r'] }, msg: { type: database, case_id: caseId, path: currentPath, relation: relationStr, prefix: value }}],
          (res) => {
            const results = [];
            const resKeys = Object.keys(res);
            if (resKeys.length > 0) {
              const rowStr = `${tools.md5(this.calculateDataBse(database) + ';' + currentPath + (relationStr ? ';' + relationStr : ''))};${this.calculateDataBse(database)};`;
              resKeys.forEach(key => {
                if (res[key].relation) { results.push({ row: key, columnValues: { title: key.split(rowStr).join(''), path: currentPath, r: res[key].relation }}); }
              });
            }
            resolve({ keys: resKeys, items: results });
          },
          () => resolve({ keys: [], items: [] })
        );
      });

      // 4. 合并并去重
      if (prefixResult.items.length > 0) {
        const currentRows = new Set(this.combinedSearchList.map(item => item.row));
        const newItems = prefixResult.items.filter(item => !currentRows.has(item.row));
        this.combinedSearchList.push(...newItems);
      }

      this.searchPrefixLoad = false;

      // 5. 更新状态并决定下一步
      if (prefixResult.keys.length < 20) {
        // 当前路径已到底，准备下一个路径
        this.prefixSearchPathIndex++;
        this.searchData.prefixRow = [];
        
        // **关键修复点**：如果当前列表还未填满，则立即自动调用自身去搜索下一个路径
        if (this.combinedSearchList.length < 20) {
          this.fetchMorePrefixResults();
        }

      } else {
        // 当前路径还有数据，更新分页标记，等待下一次滚动
        this.searchData.prefixRow = [prefixResult.keys[prefixResult.keys.length - 1]];
      }
    },

    /** 新增： 数据内容搜索函数 */
    fetchSearchData(drillDownInfo = null) {
      const { $data } = useLoginStore().wsMethod;
      let { value, database, caseId, pathArr, dataRow, relationStr } = this.searchData;
      let pathForQuery = pathArr[this.dataSearchPathIndex];
      let relationForQuery = relationStr;

      if (drillDownInfo) {
        // 如果是下钻搜索，使用下钻信息覆盖默认参数
        pathForQuery = drillDownInfo.path;
        relationForQuery = (relationStr ? relationStr + ';' : '') + drillDownInfo.relation;
        value = '';
        this.dataRow = [];
        this.dataSearchList = [];
        this.dataSearchOver = false;
      }
      $data.sendData(
        'Api.Search.SearchPrefixComp.Query',
        [
          {
            head: {
              row_key: this.dataRow, // 使用 this.dataRow 进行分页
              size: 20,
              family: ['d', 'h']
            },
            msg: {
              type: database,
              case_id: caseId,
              path: pathForQuery,
              relation: relationForQuery,
              prefix: value // 使用更新后的 value
            }
          }
        ],
        (res) => {
          console.log('搜索数据内容', res);
          if (Object.keys(res).length !== 0) {
            const resultArray = Object.entries(res).map(([key, value]) => ({ [key]: value }));
            resultArray.forEach((item) => {
              let key = Object.keys(item)[0]
              let tmpItem = {}
              tmpItem.columnValues = {}
              tmpItem.columnValues.path = pathArr[this.dataSearchPathIndex]
              if (item[key].hasOwnProperty('data')) {
                tmpItem.columnValues.d = item[key].data
                this.dataSearchList.push(tools.dataPreprocessing(tmpItem))
              }
              if (item[key].hasOwnProperty('hbase')) {
                tmpItem.columnValues.d = item[key].hbase.columnValues.d 
                this.dataSearchList.push(tools.dataPreprocessing(tmpItem))
              }
            })
            this.searchData.dataRow = [Object.keys(resultArray[resultArray.length - 1])[0]]
            // this.sendDataLast(resultArray) // (这部分逻辑保持不变)
          }

          // 分页和路径切换逻辑，只在非下钻模式下执行
          if (!drillDownInfo && this.dataSearchList.length < this.dataSizeAll) {
            if (this.dataSearchPathIndex === pathArr.length - 1) {
              Message.success({
                background: true,
                content: '已搜索路径下全部数据！'
              })
              this.searchLastLoad = false
              this.dataSearchOver = true
            } else {
              this.dataSearchPathIndex++
              this.searchData.dataRow = []
              this.getSearchData()
            }
          } else {
            this.dataSizeAll += 20
            this.searchLastLoad = false
            // 如果是下钻搜索，也需要处理结束状态
            if (drillDownInfo && Object.keys(res).length < 20) {
              this.dataSearchOver = true;
            }
          }
        }
      )
    },

    // 前缀搜索数据类型
    getPrefixSearchRelation() {
      const { $data } = useLoginStore().wsMethod
      const { value, database, caseId, pathArr, prefixRow, relationStr } = this.searchData
			console.log('<getPrefixSearchRelation> searchData:', this.searchData);

      $data.sendData(
        'Api.Search.SearchPrefixComp.Query',
        [
          {
            head: {
              row_key: prefixRow,
              size: 20,
              family: ['r']
            },
            msg: {
              type: database,
              case_id: caseId,
              path: pathArr[this.prefixSearchPathIndex],
              relation: relationStr,
              prefix: value
            }
          }
        ],
        (res) => {
					console.log('<getPrefixSearchRelation> res:', res);

					const resKeys = Object.keys(res)
          if (resKeys.length !== 0) {
            // this.searchData.prefixRow = [res[res.length - 1].row]
						this.searchData.prefixRow = [resKeys[resKeys.length - 1]]
            const pathStr = `${this.calculateDataBse(database)};${pathArr[this.prefixSearchPathIndex]}${relationStr ? ';' + relationStr : ''}`
            // const rowStr = `${tools.sha512(pathStr)};${pathStr};`
						const rowStr = `${tools.md5(pathStr)};${this.calculateDataBse(database)};`
						
						console.log('<getPrefixSearchRelation> pathStr:', pathStr);
						console.log('<getPrefixSearchRelation> rowStr:', rowStr);
            // res.forEach((item) => {
            //   const title = item.row.split(rowStr).join('')
            //   item.columnValues.title = title
            //   item.columnValues.path = pathArr[this.prefixSearchPathIndex]
            //   if (item.columnValues?.r) {
            //     this.prefixSearchList.push(item)
            //   }
            // })
						resKeys.forEach(key=>{
							if (res[key].relation) {
								let item = {}
								item.row = key
								item.columnValues = {}
								const title = key.split(rowStr).join('')
								item.columnValues.title = title
								item.columnValues.path = pathArr[this.prefixSearchPathIndex]
								item.columnValues.r = res[key].relation
								
                this.prefixSearchList.push(item)
              }
						})
						console.log("<getPrefixSearchRelation> prefixSearchList:", this.prefixSearchList);
          }
          if (resKeys.length < 20) {
            if (this.prefixSearchPathIndex === pathArr.length - 1) {
              Message.success({
                background: true,
                content: '已搜索路径下全部类型！'
              })
              this.searchPrefixLoad = false
              this.prefixSeachOver = true
            } else {
              this.prefixSearchPathIndex++
              this.searchData.prefixRow = []
              this.getPrefixSearchRelation()
            }
          } else {
            this.searchPrefixLoad = false
          }
        }
      )
    },
    // 精确搜索数据类型
    getPreciseSearchRelation() {
      const { $data } = useLoginStore().wsMethod
      const { value, database, caseId, pathArr, relationStr } = this.searchData

			console.log("<getPreciseSearchRelation> searchData:", this.searchData);
			
      pathArr.forEach((path) => {
        const pathRow =
          tools.md5(
            this.calculateDataBse(database) + ';' + path + (relationStr ? ';' + relationStr : '')
          ) +
          ';' +
          this.calculateDataBse(database) +
          (relationStr ? ';' + relationStr : '') +
          ';' +
          value
        $data.sendData(
          'Api.Search.SearchPrefixComp.DetailMulti',
          [
            {
              head: {
                row_key: [pathRow],
                size: 20
              },
              msg: {
                type: database,
                case_id: caseId,
                path: path,
                relation: relationStr,
                prefix: value
              }
            }
          ],
          (res) => {
						console.log("<getPreciseSearchRelation> res:", res);

						const resKeys = Object.keys(res)
            if (resKeys.length !== 0) {
							resKeys.forEach(key=>{
								let item = {}
								item.row = key
								item.columnValues = {}
								item.columnValues.title = value
								item.columnValues.path = path
								
								if (res[key].relation) {
									item.columnValues.r = res[key].relation
									this.preciseSearchList.push(item)

									console.log("<getPreciseSearchRelation> preciseSearchList:", this.preciseSearchList);
								}else if (res[key].hbase){
									console.log("<getPreciseSearchRelation> get hbase \n\n\n\n\n\n\n\n");
									// this.dataSearchList.push(...res)
									// this.sendDataLast(res)
								}
							})
						}
          }
        )
      })
    },
    // 搜索数据内容
    getSearchData() {
      const { $data } = useLoginStore().wsMethod
      const { value, database, caseId, pathArr, dataRow, relationStr } = this.searchData
      $data.sendData(
        'Api.Search.SearchPrefixComp.Query',
        [
          {
            head: {
              row_key: dataRow,
              size: 20,
              family: ['d', 'h']
            },
            msg: {
              type: database,
              case_id: caseId,
              path: pathArr[this.dataSearchPathIndex],
              relation: relationStr,
              prefix: value
            }
          }
        ],
        (res) => {
          console.log('搜索数据内容', res);
          if (Object.keys(res).length !== 0) {
            const resultArray = Object.entries(res).map(([key, value]) => ({ [key]: value }));
            resultArray.forEach((item) => {
              let key = Object.keys(item)[0]
              let tmpItem = {}
              tmpItem.columnValues = {}
              tmpItem.columnValues.path = pathArr[this.dataSearchPathIndex]
              // item[key].hbase.columnValues.d = item[key].hbase.columnValues.d
              // item[key].hbase.columnValues.path = pathArr[this.dataSearchPathIndex]
              if (item[key].hasOwnProperty('data')) {
                tmpItem.columnValues.d = item[key].data
                this.dataSearchList.push(tools.dataPreprocessing(tmpItem))
              }
              if (item[key].hasOwnProperty('hbase')) {
                tmpItem.columnValues.d = item[key].hbase.columnValues.d 
                this.dataSearchList.push(tools.dataPreprocessing(tmpItem))
              }
            })
            this.searchData.dataRow = [Object.keys(resultArray[resultArray.length - 1])[0]]
            // this.sendDataLast(resultArray)
          }
          if (this.dataSearchList.length < this.dataSizeAll) {
            if (this.dataSearchPathIndex === pathArr.length - 1) {
              Message.success({
                background: true,
                content: '已搜索路径下全部数据！'
              })
              this.searchLastLoad = false
              this.dataSearchOver = true
            } else {
              this.dataSearchPathIndex++
              this.searchData.dataRow = []
              this.getSearchData()
            }
          } else {
            this.dataSizeAll += 20
            this.searchLastLoad = false
          }
        }
      )
    },
    // 搜索数据下是否还有一层
    sendDataLast(res) {
      const { $data } = useLoginStore().wsMethod
      const { database, caseId, relationStr } = this.searchData
      let dataLastObj = {}
      res.forEach((item) => {
        let keys = Object.keys(item)[0]
        console.log("<sendDataLast> item:",item, keys, item[keys]);
        const dataObj = item[keys].hbase.columnValues.d
        for (const key in dataObj) {
          if (Object.hasOwnProperty.call(dataObj, key)) {
            const value = dataObj[key]
            if (tools.isPlainObject(value)) {
              if (!Object.hasOwnProperty.call(this.dataHaveLastObj, key)) {
                dataLastObj[key] = item[keys].hbase.columnValues.d.path
              }
              if (!Object.hasOwnProperty.call(this.dataHaveLastObj, value)) {
                dataLastObj[value] = item[keys].hbase.columnValues.d.path
              }
            }
          }
        }
      })
      for (const key in dataLastObj) {
        if (Object.hasOwnProperty.call(dataLastObj, key)) {
          const value = dataLastObj[key]
          $data.sendData(
            'Api.Search.SearchPrefixComp.Query',
            [
              {
                head: {
                  row_key: [],
                  size: 20,
                  family: ['d', 'h']
                },
                msg: {
                  type: database,
                  case_id: caseId,
                  path: value,
                  relation: relationStr ? relationStr + ';' + key : key,
                  prefix: ''
                }
              }
            ],
            (res) => {
              if (res?.length) {
                this.dataHaveLastObj[key] = value
              }
            }
          )
        }
      }
    },
    // 向爬虫发送统计任务
    sendStatisticsPath() {
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.DataAnalysisTask.SendAsyncTask',
        [
          {
            head: {},
            msg: {
              key: 'query_data',
              topic: 'ParseTools.Database.HbaseDataCountComp',
              value: {}
            }
          }
        ],
        (res) => {
          if (res?.status === 'ok') {
            Message.success({
              background: true,
              content: '添加统计任务成功！'
            })
          } else {
            Message.success({
              background: true,
              content: '添加统计任务失败！'
            })
          }
        }
      )
    },
    // 获取所有路径下的数据量
    getAllPathDataNum() {
      const { $zook } = useLoginStore().wsMethod
      $zook.sendData(
        'Api.Node.NodeData',
        [
          {
            msg: {
              '/etc/kappa/hbase_query_data_comp/path_count_num_result': ''
            }
          }
        ],
        (res) => {
          console.log("总数据量:",res);
          const resData = res['/etc/kappa/hbase_query_data_comp/path_count_num_result']
          const countNumRes = resData.count_num_obj
          const timestamp = Number(resData.timestamp)
          this.pathNumObj.countRes.num = countNumRes.d?.count
          this.pathNumObj.countRes.indexNum = (countNumRes.r?.count ?? 0) + (countNumRes.h?.count ?? 0)
          this.pathNumObj.countRes.timestamp = timestamp
        }
      )
      $zook.sendData(
        'Api.Node.NodeData',
        [
          {
            msg: {
              '/etc/kappa/hbase_query_data_comp/path_count_num': ''
            }
          }
        ],
        (res) => {
          console.log("索引数据量:", res);
          const resData = res['/etc/kappa/hbase_query_data_comp/path_count_num']
          const countNum = resData.count_num_obj
          const timestamp = Number(resData.timestamp)
          this.pathNumObj.countSta.num = countNum.d?.count
          this.pathNumObj.countSta.indexNum = (countNum.h?.count ?? 0) + (countNum.r?.count ?? 0)
          this.pathNumObj.countSta.timestamp = timestamp
        }
      )
    },
    // 设置所有路径下的数据量
    setPathDataNum() {
      let allPathTreeData = this.allPathTreeData
      const that = this
      function traverseTree(data) {
        let title = ''
        const parenthesisIndex = data.title.indexOf('(')
        if (parenthesisIndex !== -1) {
          title = data.title.substring(0, parenthesisIndex)
        } else {
          title = data.title
        }
        let path = data.value
        if (that.pathCountNum[path]) {
          data.title = `${title} (${tools.formatNumber(that.pathCountNum[path])}条)`
        }
        if (data.children && data.children.length > 0) {
          for (const child of data.children) {
            traverseTree(child)
          }
        }
      }
      for (const child of allPathTreeData) {
        traverseTree(child)
      }
      this.allPathTreeData = allPathTreeData
    }
  }
})
