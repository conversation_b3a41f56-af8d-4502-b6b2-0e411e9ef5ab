import { defineStore } from 'pinia'
import { useLoginStore } from './login'
import { Message } from 'view-ui-plus'
import tools from '@/utils/tools'

export const useAnalysisStore = defineStore('analysisStore', {
  state: () => ({
    OssAnalysisTemplate: [],
    ossAnalysisLoading: true,
    ossAnalysisField: [],
    OssDataTypeList: [],
    OssDisposeLoad: true,
    OssDataTypeLoad: true,
    databaseRelation: {
      database: '',
      caseId: '',
      path: '',
      value: ''
    },
    logicFlowData: {
      // 节点
      nodes: [
        {
          id: 'PoWGf7rw2nut',
          type: 'database-node',
          x: 200,
          y: 300,
          properties: {
            initialPoint: true,
            name: '',
            relStr: ''
          }
        }
      ],
      edges: []
    },
    logicFlowLoading: true,
    parseTaskList: [],
    parsingTaskList: [],
    parseReadyTaskList: [],
    parseTaskLogs: [],
    showTaskType: 'parse_dbfile_quick_task'
  }),
  actions: {
    //设置任务类型
    setTaskType(param) {
      param == 'default'
        ? (this.showTaskType = 'parse_dbfile_quick_task')
        : (this.showTaskType = 'parse_dbfile_quick_task_csv_comp')
    },

    // 解析模板管理
    // 获取解析模板
    getAnalysisOss(data) {
      this.ossAnalysisLoading = true
      this.OssAnalysisTemplate = []
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.Search.SearchList.Query',
        [
          {
            head: {
              size: 200
            },
            control: {
              query_string: '',
              query_type: 'username',
              condition: {
                query_mode: 'match'
              },
              add_es_query_conditions: data
            },
            msg: {
              data_range_index_name: 'parsing_template'
            }
          }
        ],
        (res) => {
          this.ossAnalysisLoading = false
          this.OssAnalysisTemplate = res.hits.hits
        }
      )
    },
    // 创建oss表
    createAnalysisOss() {
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.Search.SearchList.CreateOss',
        [
          {
            head: {},
            control: {
              query_type: 'username',
              index: 'parsing_template',
              type: '_doc',
              id: 'parsing',
              field_template: {
                title: {
                  type: 'keyword',
                  index: true
                },
                analysis_field: {
                  type: 'text',
                  index: false
                }
              }
            }
          }
        ],
        (res) => {
          if (res.status === 'ok') {
            console.log('创建Oss储存表成功')
          }
        }
      )
    },
    // 删除oss表
    dropAnalysisOss() {
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.Search.SearchList.DropOss',
        [
          {
            head: {},
            control: {
              query_type: 'username',
              index: 'parsing_template',
              type: '_doc',
              id: 'parsing'
            }
          }
        ],
        () => {}
      )
    },
    // 向oss表中添加数据
    sendAddAnalysisOss(data) {
      this.ossAnalysisLoading = true
      const { $data } = useLoginStore().wsMethod
      const { title, fileNameArr, id } = data
      $data.sendData(
        'Api.Search.SearchList.AddOss',
        [
          {
            head: {},
            control: {
              query_type: 'username',
              index: 'parsing_template',
              type: '_doc',
              id: id
            },
            msg: {
              type: 'parsing_data',
              title: title,
              analysis_field: JSON.stringify(fileNameArr)
            }
          }
        ],
        (res) => {
          if (res?.status !== 'ok') {
            Message.error({
              background: true,
              content: '模板保存失败'
            })
          }
        }
      )
    },

    //获取解析模板并处理数据格式
    getAnalysisOssDispose() {
      this.ossAnalysisField = []
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.Search.SearchList.Query',
        [
          {
            head: {
              size: 200
            },
            control: {
              query_string: '',
              query_type: 'username',
              condition: {
                query_mode: 'match'
              }
            },
            msg: {
              data_range_index_name: 'parsing_template'
            }
          }
        ],
        (res) => {
          res?.hits?.hits?.forEach((item) => {
            this.ossAnalysisField.push({
              value: item._source.analysis_field,
              label: item._source.title
            })
          })
          this.OssDisposeLoad = false
        },
        (error) => {
          if (error.includes('not found')) {
            this.createAnalysisOss()
          }
        }
      )
    },
    // 初始化搜索入库路径的搜索条件
    calculateDataBse(type) {
      const { userinfo } = useLoginStore()
      switch (type) {
        case 'public':
          return type.charAt(0)
        case 'authority':
          return type.charAt(0) + ';' + userinfo.authority
        case 'username':
          return type.charAt(0) + ';' + userinfo.authority + ';' + userinfo.username
        case 'case':
          return type.charAt(0) + ';' + this.databaseRelation.caseId
        default:
          return
      }
    },
    initSearchDatabase(data) {
      this.logicFlowLoading = true
      if (data.database) {
        this.databaseRelation.database = data.database
        this.databaseRelation.caseId = data.caseId
        this.databaseRelation.path = data.path
      }
      this.databaseRelation.value = data.value
      this.logicFlowData = {
        // 节点
        nodes: [
          {
            id: 'PoWGf7rw2nut',
            type: 'database-node',
            x: 200,
            y: 300,
            properties: {
              initialPoint: true,
              name: data.value,
              relStr: data.value
            }
          }
        ],
        edges: []
      }
      this.getGraphDatabase()
    },
    // 搜索入库路径下的图数据库
    getGraphDatabase() {
      const { $data } = useLoginStore().wsMethod
      const { database, caseId, path, value } = this.databaseRelation
      const pathRowPrefix = this.calculateDataBse(database)
      let timer = null
      let sendIndex = 0
      const recursion = (relStr, lastNodeId, indX) => {
        const pathRow =
          tools.sha512(pathRowPrefix + ';' + path + (relStr ? ';' + relStr : '')) +
          ';' +
          pathRowPrefix +
          ';' +
          path +
          (relStr ? ';' + relStr : '') +
          ';' +
          value
        sendIndex++
        $data.sendData(
          'Api.Search.SearchPrefix.DetailMulti',
          [
            {
              head: {
                row_key: [pathRow],
                size: 20
              },
              msg: {
                type: database,
                case_id: caseId,
                path: path,
                relation: relStr,
                prefix: value
              }
            }
          ],
          (res) => {
            sendIndex--
            if (res[0].columnValues?.r) {
              const obj = res[0].columnValues.r
              const relTypeArr = Object.keys(obj)
              relTypeArr.forEach((key, indY) => {
                const nodeID = tools.generateRandomString()
                let newRelStr = ''
                if (relStr) {
                  newRelStr = relStr + ';' + obj[key].name
                } else {
                  newRelStr = value + ';' + obj[key].name
                }
                this.logicFlowData.nodes.push({
                  id: nodeID,
                  type: 'database-node',
                  x: 200 + 300 * indX,
                  y: 300 + 200 * indY,
                  properties: {
                    relStr: newRelStr,
                    ...obj[key]
                  }
                })
                this.logicFlowData.edges.push({
                  type: 'bezier',
                  sourceNodeId: lastNodeId,
                  targetNodeId: nodeID
                })

                recursion(newRelStr, nodeID, indX + 1)
              })
            }
          }
        )
      }
      recursion('', this.logicFlowData.nodes[0].id, 1)
      timer = setInterval(() => {
        if (sendIndex <= 0) {
          clearInterval(timer)
          this.logicFlowLoading = false
        }
      }, 100)
    },
    // 获取数据类型列表
    getDataTypeOss() {
      this.OssDataTypeList = []
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.Search.SearchList.Query',
        [
          {
            head: {
              size: 200
            },
            control: {
              query_string: '',
              query_type: 'username',
              condition: {
                query_mode: 'match'
              }
            },
            msg: {
              data_range_index_name: 'parsing_data_type'
            }
          }
        ],
        (res) => {
          this.OssDataTypeLoad = false
          this.OssDataTypeList = res.hits.hits
        }
      )
    },
    // 创建oss数据类型表
    createDataTypeOss() {
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.Search.SearchList.CreateOss',
        [
          {
            head: {},
            control: {
              query_type: 'username',
              index: 'parsing_data_type',
              type: '_doc',
              id: 'data_type',
              field_template: {
                icon: {
                  type: 'keyword',
                  index: false
                },
                name: {
                  type: 'keyword',
                  index: true
                },
                nickname: {
                  type: 'keyword',
                  index: true
                },
                detail: {
                  type: 'keyword',
                  index: false
                }
              }
            }
          }
        ],
        (res) => {
          if (res.status === 'ok') {
            console.log('创建Oss数据类型储存表成功')
          }
        }
      )
    },
    // 新增数据类型
    sendAddDataTyprOss(data) {
      this.ossAnalysisLoading = true
      const { $data } = useLoginStore().wsMethod
      const { icon, name, nickname, detail, id } = data
      $data.sendData(
        'Api.Search.SearchList.AddOss',
        [
          {
            head: {},
            control: {
              query_type: 'username',
              index: 'parsing_data_type',
              type: '_doc',
              id: id
            },
            msg: {
              type: 'data_type',
              icon: icon,
              name: name,
              nickname: nickname,
              detail: detail
            }
          }
        ],
        (res) => {
          if (res?.status !== 'ok') {
            Message.error({
              background: true,
              content: '数据类型保存失败'
            })
          }
        }
      )
    },
    // 添加解析任务
    sendAddParseFileTask(data) {
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.DataAnalysisTask.AddSimpleTask',
        [
          {
            head: {},
            msg: {
              title: data.taskName,
              task_type: this.showTaskType,
              case_id: data.caseId,
              method: data.fileType,
              task_authority: 'username',
              parms: {
                data_type: data.data_type,
                fields: data.fields,
                file_path: data.databasePath,
                file_type: data.databaseType,
                parse_authority_type: data.parseDataBase,
                parse_big_data_path: data.parsePath,
                parse_big_data_relation: data.parseRelation,
                separator: data.decollator,
                simple_clean: data.simpleClean
              }
            }
          }
        ],
        (res) => {
          if (res?.status === 'ok') {
            Message.success({
              background: true,
              content: '添加解析任务成功'
            })
            this.getParsingTaskList()
          } else {
            Message.error({
              background: true,
              content: '添加解析任务失败'
            })
          }
        }
      )
    },
    // 添加csv解析任务
    sendAddParseCsvFileTask(data) {
      console.log('sendAddParseCsvFileTask:', data)
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.DataAnalysisTask.AddSimpleTask',
        [
          {
            head: {},
            msg: {
              title: data.taskName,
              task_type: 'parse_dbfile_quick_task_csv_comp',
              method: data.databaseType,
              task_authority: 'username',
              parms: {
                file_path: data.databasePath,
                file_type: data.databaseType,
                separator: data.decollator,
                parse_big_data_path: data.parsePath
              }
            }
          }
        ],
        (res) => {
          if (res?.status === 'ok') {
            Message.success({
              background: true,
              content: '添加解析任务成功'
            })
            this.getParsingTaskList()
          } else {
            Message.error({
              background: true,
              content: '添加解析任务失败'
            })
          }
        }
      )
    },
    // 重新分析
    sendTaskReanalyse(data) {
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.DataAnalysisTask.SetStatus',
        [
          {
            head: {
              row_key: [data]
            },
            msg: {
              task_type: this.showTaskType,
              task_authority: 'username',
              status: 'parse_ready'
            }
          }
        ],
        (res) => {
          if (res?.status === 'ok') {
            Message.success({
              background: true,
              content: '开始重新分析'
            })
          }
        }
      )
    },
    //重新分析
    // 查看解析任务列表
    getParseTaskList() {
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.DataAnalysisTask.List',
        [
          {
            head: {
              size: 200,
              row_key: []
            },
            msg: {
              task_authority: 'username',
              task_type: this.showTaskType,
              status: [],
              familys: ['parm', 'info']
            }
          }
        ],
        (res) => {
          this.parseTaskList = res
        }
      )
    },
    // 获取正在解析和准备解析的任务
    getParsingTaskList(type) {
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.DataAnalysisTask.List',
        [
          {
            head: {
              size: 10,
              row_key: []
            },
            msg: {
              task_authority: 'username',
              task_type: this.showTaskType,
              status: ['parsing'],
              familys: ['parm', 'info']
            }
          }
        ],
        (res) => {
          this.parsingTaskList = res
        }
      )
      $data.sendData(
        'Api.DataAnalysisTask.List',
        [
          {
            head: {
              size: 10,
              row_key: []
            },
            msg: {
              task_authority: 'username',
              task_type: this.showTaskType,
              status: ['parse_ready'],
              familys: ['parm', 'info']
            }
          }
        ],
        (res) => {
          this.parseReadyTaskList = res
        }
      )
    },
    // 删除任务
    sendDelParseTask(data) {
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.DataAnalysisTask.Del',
        [
          {
            head: {
              row_key: [data]
            },
            msg: {
              task_authority: 'username',
              task_type: this.showTaskType
            }
          }
        ],
        (res) => {
          if (res?.status === 'ok') {
            Message.success({
              background: true,
              content: '删除任务成功'
            })
          } else {
            Message.error({
              background: true,
              content: '删除任务失败'
            })
          }
          this.getParseTaskList()
        }
      )
    },
    // 查看任务日志
    sendParseTaskLogs(data) {
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.DataAnalysisTask.ListLogs',
        [
          {
            head: {
              row_key: [],
              size: 200
            },
            msg: {
              task_authority: 'username',
              task_id: data,
              task_type: this.showTaskType
            }
          }
        ],
        (res) => {
          this.parseTaskLogs = res
        }
      )
    }
  }
})
