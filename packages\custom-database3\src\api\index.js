import register from 'certificate-login'
import dcWebsocket from 'dc-websocket-jsonrpc'
import { useLoginStore } from '@/stores/login'
import router from '@/router/index'

export const LoadCertificateCommunication = (app) => {
  // 挂载serviceWorker
  // 传入参数为一个数组，两个回调函数
  // 第一个参数是设置fetch请求拦截重新发送的,是一个数组，每个值为fetch请求的前缀值
  // 第二个回调函数在收到加密狗推送消息DC_PUSH_WEB后执行的操作
  // 第三个回调函数在收到加密狗推送消息Error后自定义的遮罩层，不传入显示默认遮罩层
  register(
    [
      '/filesystem/api/rest/v2/node-0/small_file',
      '/filesystem/api/rest/v2/node-0/big_file',
      '/filesystem/api/rest/v2/node-0/main_file'
    ],
    () => {
      const nowRoutePath = router.options.history.location
      if (nowRoutePath === '/login') {
        console.log('跳转页面')
        router.push('/index')
      } else {
        console.log('重新安装通信结果')
        installWebsocket()
      }
    }
  )
  // 建立websockt连接
  const { WebSocketClient } = dcWebsocket
  const ws_list = {
    $data: '/data_analysis_platform/api/ws-jsonrpc/v1',
    $case: '/case/api/ws-jsonrpc/v2',
    $pki: '/pki/api/ws-jsonrpc/v1',
    $file: '/filesystem/api/ws-jsonrpc/v2',
    $zook: '/websocket/constant_data/api/ws-jsonrpc/v1'
  }
  const installWebsocket = () => {
    const wsStore = useLoginStore()
    Object.keys(ws_list).map((wsName) => {
      const url = 'wss://' + window.location.host + ws_list[wsName]
      const socketClient = new WebSocketClient(url, {
        reconnectEnabled: true, // 是否重连
        reconnectInterval: 3000, // 重连间隔时间
        recconectAttempts: 99 // 重连次数,超出了就停止重连
      })
      socketClient.connect()
      // 连接成功回调
      socketClient.onOpen = () => {
        wsStore.socket_on_open()
      }
      // 消息接收回调  msg为接收到的原生消息参数
      socketClient.onMessage = (msg) => {
        wsStore.socket_on_message(msg)
      }
      // 连接关闭回调  e为连接关闭的回调参数
      socketClient.onClose = () => {
        wsStore.socket_on_close()
      }
      // 连接错误回调  e为连接错误的回调参数
      socketClient.onError = (e) => {
        wsStore.socket_on_error(e)
      }
      // 重连的回调  num为重连的次数
      socketClient.onReconnect = (num) => {
        wsStore.socket_reconnect(num)
      }
      // 重连超过设置连接次数的回调
      socketClient.onReconnectError = () => {
        wsStore.socket_reconnect_error()
      }

      wsStore.wsMethod[wsName] = socketClient
      app.provide(wsName, socketClient)
      // 开发调试使用
      window[wsName] = socketClient
    })
  }
  installWebsocket()
}
