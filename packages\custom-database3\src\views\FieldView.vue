<template>
  <div class="field_header">
    <p class="header_text">数据分类
      <p class="tip">提示: 当操作添加或删除字段后，在使用快速查询时需要在选择数据类型或搜索前点击选择框右侧刷新按钮。</p>
    </p>
    <div class="his_path_body">
      <div class="field_path">
        <b>查询路径:</b>
        <div class="path_list">
          <div v-for="path in checkFieldPathArr" :key="path">
            <Tag
              @click="historyFieldPathClick(path)"
              :class="path === nowCheckFieldPath ? 'active_path path_item' : 'path_item'"
              type="dot"
              color="primary"
              >{{ path }}</Tag
            >
            <Icon
              v-if="path !== checkFieldPathArr[checkFieldPathArr.length - 1]"
              type="ios-arrow-forward"
            />
          </div>
        </div>
      </div>
      <Button type="dashed" @click="copyFieldPath">复制字段</Button>
    </div>
  </div>
  <div class="field_body">
    <div class="field_card" v-for="item in fieldStore.fieldPathList" :key="item.row">
      <Card class="card" :padding="10" @dblclick="cardFieldPathClick(item.columnValues.i)">
        <template #title>{{ item.columnValues.i.name }}</template>
        <div class="card_body">
          <div class="card_desc" v-if="item.columnValues?.p?.alias">
            <b>名称：</b>
            <p class="desc_name" :title="item.columnValues?.p?.alias">
              {{ item.columnValues.p.alias }}
            </p>
          </div>
          <div class="card_desc" v-if="item.columnValues?.p?.summary">
            <b>描述：</b>
            <p class="desc_body" :title="item.columnValues?.p?.summary">
              {{ item.columnValues.p.summary }}
            </p>
          </div>
        </div>
      </Card>
      <div class="card_btn" v-if="item.columnValues?.p && item.columnValues.p.sys_lock === 'true'">
        <div class="prec">
          <Icon type="md-alert" size="24" color="#f0c758" />
          <span>系统锁定</span>
        </div>
      </div>

      <div class="card_btn" v-else>
        <div>
          <template v-if="item.columnValues?.p?.lock === 'false'">
            <Icon
              title="编辑字段"
              type="ios-create"
              size="24"
              color="#2d8cf0"
              @click.stop="clickEditField(item)"
            />
            <Icon
              type="ios-trash"
              size="24"
              title="删除字段"
              color="#e43922"
              @click="clickDeleteField(item.columnValues.i.path)"
            />
          </template>
        </div>
        <Icon
          :type="item.columnValues?.p?.lock === 'false' ? 'md-unlock' : 'md-lock'"
          size="24"
          color="#bfc206"
          :title="item.columnValues?.p?.lock === 'false' ? '锁定字段' : '解锁字段'"
          @click="clickLockField(item)"
        />
      </div>
    </div>
    <div class="field_card">
      <div class="add_card">
        <div class="icon_add" @click="showAddFieldModal">
          <Icon type="ios-add" size="100" />
        </div>
      </div>
    </div>
  </div>
  <!-- 添加字段对话框 -->
  <Modal v-model="addFieldPathModal" title="添加字段" :mask-closable="false">
    <Form
      ref="addFieldFromRef"
      :model="addFieldFrom"
      :label-width="80"
      label-colon
      :rules="addFieldRule"
    >
      <FormItem label="字段名" prop="name">
        <Input v-model="addFieldFrom.name" placeholder="请输入字段名"></Input>
      </FormItem>
      <FormItem label="中文名" prop="cnName">
        <Input v-model="addFieldFrom.cnName" placeholder="请输入字段中文名"></Input>
      </FormItem>
      <FormItem label="描述" prop="describe">
        <Input v-model="addFieldFrom.describe" type="textarea" placeholder="请输入字段描述"></Input>
      </FormItem>
      <FormItem label="字段锁" prop="isLock">
        <Switch size="large" v-model="addFieldFrom.isLock">
          <template #open>
            <span>锁定</span>
          </template>
          <template #close>
            <span>解锁</span>
          </template>
        </Switch>
      </FormItem>
    </Form>
    <template #footer>
      <Button type="text" @click="resetAddFieldFrom">重 置</Button>
      <Button type="primary" @click="submitAddFieldFrom">添 加</Button>
    </template>
  </Modal>
  <!-- 修改字段对话框 -->
  <Modal v-model="editFieldPathModal" title="修改字段" :mask-closable="false">
    <Form
      ref="editFieldFromRef"
      :model="editFieldFrom"
      :label-width="80"
      label-colon
      :rules="edteFieldRule"
    >
      <FormItem label="中文名" prop="cnName">
        <Input v-model="editFieldFrom.cnName" placeholder="请输入字段中文名"></Input>
      </FormItem>
      <FormItem label="描述" prop="describe">
        <Input
          v-model="editFieldFrom.describe"
          type="textarea"
          placeholder="请输入字段描述"
        ></Input>
      </FormItem>
    </Form>
    <template #footer>
      <Button type="text" @click="resetEditFieldFrom">重 置</Button>
      <Button type="primary" @click="submitEditFieldFrom">确 定</Button>
    </template>
  </Modal>
</template>

<script setup>
import { useFieldStore } from '@/stores/field'
import { onMounted, reactive, ref, getCurrentInstance } from 'vue'
import { Modal, Message } from 'view-ui-plus'

const fieldStore = useFieldStore()
// 查询路径
const checkFieldPathObject = reactive({ '/': '/' })
const checkFieldPathArr = ref(['/'])
const nowCheckFieldPath = ref('/')
onMounted(() => {
  fieldStore.getFieldPathList('/')
})
// 复制查询字段
const instance = getCurrentInstance()
const $Copy = instance.appContext.config.globalProperties.$Copy
const copyFieldPath = () => {
  $Copy({
    text: checkFieldPathObject[nowCheckFieldPath.value]
  })
}
// 点击历史记录查询
const historyFieldPathClick = (path) => {
  const index = checkFieldPathArr.value.indexOf(path) + 1
  const deletePathArr = checkFieldPathArr.value.splice(index)
  deletePathArr.forEach((path) => {
    delete checkFieldPathObject[path]
  })
  nowCheckFieldPath.value = path
  fieldStore.getFieldPathList(checkFieldPathObject[path])
}
// 点击卡片查询
const cardFieldPathClick = (item) => {
  checkFieldPathObject[item.name] = item.path
  checkFieldPathArr.value.push(item.name)
  nowCheckFieldPath.value = item.name
  fieldStore.getFieldPathList(item.path)
}
// 对字段的操作
// 添加字段
const addFieldPathModal = ref(false)
const addFieldFromRef = ref(null)
const addFieldFrom = reactive({
  name: '',
  cnName: '',
  describe: '',
  isLock: false
})
const addFieldRule = reactive({
  name: [{ required: true, message: '字段名为必填项', trigger: 'blur' }],
  cnName: [{ required: true, message: '字段中文名为必填项', trigger: 'blur' }]
})
const showAddFieldModal = () => {
  addFieldPathModal.value = true
}
const resetAddFieldFrom = () => {
  addFieldFromRef.value.resetFields()
}
const submitAddFieldFrom = () => {
  addFieldFromRef.value.validate((valid) => {
    if (valid) {
      fieldStore.sendAddFieldPath(addFieldFrom)
      addFieldPathModal.value = false
    }
  })
}
// 修改字段
const editFieldPathModal = ref(false)
const editFieldFromRef = ref(null)
const editFieldFrom = reactive({
  cnName: '',
  describe: ''
})
const edteFieldRule = reactive({
  cnName: [{ required: true, message: '字段中文名为必填项', trigger: 'blur' }]
})
const nowEditFieldItem = ref(null)
const clickEditField = (item) => {
  editFieldFrom.cnName = item.columnValues.p.alias
  editFieldFrom.describe = item.columnValues.p.summary
  editFieldPathModal.value = true
  nowEditFieldItem.value = item
}
const resetEditFieldFrom = () => {
  editFieldFromRef.value.resetFields()
}
const submitEditFieldFrom = () => {
  editFieldFromRef.value.validate((valid) => {
    if (valid) {
      const editMapFrom = {
        path: nowEditFieldItem.value.columnValues.i.path,
        map: {
          alias: editFieldFrom.cnName,
          summary: editFieldFrom.describe
        },
        tip: '修改字段'
      }
      fieldStore.sendEditFirldPath(editMapFrom)
      editFieldPathModal.value = false
    }
  })
}
// 删除字段
const clickDeleteField = (path) => {
  Modal.confirm({
    title: '提示',
    content: '此操作将永久删除该字段，是否确定？',
    onOk: () => {
      fieldStore.sendDelFieldPath(path)
    },
    onCancel: () => {
      Message.info('已取消删除')
    }
  })
}
// 锁定/解锁字段
const clickLockField = (item) => {
  const editLockFrom = {
    path: item.columnValues.i.path,
    map: {
      lock: item.columnValues.p.lock === 'false' ? 'true' : 'false'
    },
    tip: item.columnValues.p.lock === 'false' ? '锁定字段' : '解锁字段'
  }
  fieldStore.sendEditFirldPath(editLockFrom)
}
</script>

<style scoped lang="scss">
.field_header {
  height: 115px;
  width: 100%;
  margin-top: 10px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .header_text {
    font-size: 24px;
    padding: 10px 0 0 10px;
    display: flex;
    align-items: center;
    .tip{
      font-size: 14px;
      color: red;
      margin-left: 30px;
    }
  }
  .his_path_body {
    display: flex;
    align-items: center;
    margin-left: 10px;
    margin-bottom: 10px;
    .field_path {
      width: 93%;
      display: flex;
      align-items: center;
      .path_list {
        margin-left: 5px;
        display: flex;
        align-items: center;
        .path_item {
          cursor: pointer;
        }
        .active_path {
          border: 1px solid #118d11 !important;
        }
        .path_item:hover {
          border: 1px solid #2d8cf0 !important;
        }
      }
    }
  }
}
.field_body {
  height: 80vh;
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  justify-content: flex-start;
  overflow: auto;
  .field_card {
    width: 13.4%;
    height: 170px;
    margin: 0 15px 15px 0;
    position: relative;
    .card {
      cursor: pointer;
      .card_body {
        height: 100px;
        .card_desc {
          display: flex;
        }
        .desc_name {
          width: 78%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .desc_body {
          width: 78%;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          line-clamp: 3;
          -webkit-box-orient: vertical;
          text-overflow: ellipsis;
        }
      }
      .card_input {
        height: 100px;
      }
    }
    .card_btn {
      position: absolute;
      border-top: 1px solid #e8eaec;
      padding: 3px;
      width: 100%;
      bottom: 0;
      left: 0;
      display: flex;
      justify-content: space-between;
      i {
        cursor: pointer;
      }
      .prec {
        display: flex;
        align-items: center;
        span {
          margin-left: 5px;
        }
      }
    }
    .add_card {
      height: 167px;
      background-color: #fff;
      border-radius: 3px;
      padding: 10px;
      .icon_add {
        width: 100%;
        height: 100%;
        cursor: pointer;
        display: grid;
        place-items: center;
      }
    }
  }
}
.field_body::-webkit-scrollbar {
  width: 0;
  height: 0;
  background-color: transparent;
}
.field_body::-webkit-scrollbar-thumb {
  background-color: transparent;
}
.field_body::-webkit-scrollbar-track {
  background-color: transparent;
}
.field_body::-webkit-scrollbar-button {
  display: none;
}
.field_body::-webkit-scrollbar-corner {
  background-color: transparent;
}
:deep(.ivu-card-head) {
  font-size: 16px;
  font-weight: bold;
}
</style>
