<template>
  <div class="layout">
    <Layout :style="{ minHeight: '100vh' }">
      <Sider ref="side1" hide-trigger collapsible :collapsed-width="78" v-model="isCollapsed">
        <Menu :active-name="loginStore.nowPath" theme="dark" width="auto" :class="menuitemClasses">
          <div class="sider_header">
            <img src="@/assets/images/logo.jpg" />
            <p v-if="!isCollapsed">智网社情分析系统</p>
          </div>
          <template v-for="router in layoutMap">
            <MenuItem
              v-if="!router.hidden"
              :name="router.path"
              :key="router.name"
              :to="router.path"
            >
              <Icon :type="router.meta.icon"></Icon>
              <span>{{ router.meta.title }}</span>
            </MenuItem>
          </template>
        </Menu>
      </Sider>
      <Layout>
        <Header :style="{ padding: 0 }" class="layout-header-bar">
          <Icon
            @click="collapsedSider"
            :class="rotateIcon"
            :style="{ margin: '0 20px' }"
            type="md-menu"
            size="24"
          ></Icon>
          <router-link :to="{ name: 'system' }" class="system"
            ><Icon type="ios-globe-outline" />内网系统</router-link
          >
          <div @click="checkUserInfo" class="user_name">
            <Icon type="ios-contact" size="28" />
            <p>{{ loginStore.userinfo.username }}</p>
          </div>
        </Header>
        <Content class="conten"> <RouterView /> </Content>
      </Layout>
    </Layout>
  </div>
  <Drawer title="用户信息" v-model="userDrawerValue" width="400">
    <Form label-position="left" :label-width="100">
      <FormItem label="用户名:">{{ loginStore.userinfo.username }}</FormItem>
      <FormItem label="用户权限:">{{ loginStore.userinfo.authority }}</FormItem>
      <FormItem label="用户ID:">{{ loginStore.userinfo.user_id }}</FormItem>
      <FormItem label="创建时间:"
        >{{ $tools.timestamp(loginStore.userinfo.create_time) }}
      </FormItem>
      <FormItem label="加密狗ID:">{{ loginStore.userinfo.superdog_id }}</FormItem>
    </Form>
  </Drawer>
</template>

<script setup>
import { RouterView } from 'vue-router'
import { ref, computed, inject } from 'vue'
import { layoutMap } from '@/router/router'
import { useLoginStore } from '@/stores/login'

const $tools = inject('$tools')

const isCollapsed = ref(false)
const rotateIcon = computed(() => ['menu-icon', isCollapsed.value ? 'rotate-icon' : ''])
const menuitemClasses = computed(() => ['menu-item', isCollapsed.value ? 'collapsed-menu' : ''])

const side1 = ref(null)
const collapsedSider = () => {
  side1.value.toggleCollapse()
}

const userDrawerValue = ref(false)
const loginStore = useLoginStore()
const checkUserInfo = () => {
  userDrawerValue.value = !userDrawerValue.value
}
</script>

<style scoped lang="scss">
.layout {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  .sider_header {
    height: 64px;
    border-bottom: 1px solid #5b6270;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    img {
      width: 35px;
      height: 35px;
      border-radius: 5px;
    }
    p {
      font-size: 14px;
      font-family: 'Courier New', Courier, monospace;
      color: #fff;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      vertical-align: bottom;
      transition: width 0.2s ease 0.2s;
    }
  }
  .conten {
    background-color: #e9e9e9;
  }
}
.layout .ivu-menu {
  z-index: 0;
}
.layout-header-bar {
  background: #fff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  position: relative;
  .user_name {
    font-size: 16px;
    position: absolute;
    top: 0;
    right: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  .system {
    font-size: 16px;
  }
}
.layout-logo-left {
  width: 90%;
  height: 30px;
  background: #5b6270;
  border-radius: 3px;
  margin: 15px auto;
}
.menu-icon {
  transition: all 0.3s;
}
.rotate-icon {
  transform: rotate(-90deg);
}
.menu-item span {
  display: inline-block;
  overflow: hidden;
  width: 100px;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
  transition: width 0.2s ease 0.2s;
}
.menu-item i {
  transform: translateX(0px);
  transition:
    font-size 0.2s ease,
    transform 0.2s ease;
  vertical-align: middle;
  font-size: 16px;
}
.collapsed-menu span {
  width: 0px;
  transition: width 0.2s ease;
}
.collapsed-menu i {
  transform: translateX(5px);
  transition:
    font-size 0.2s ease 0.2s,
    transform 0.2s ease 0.2s;
  vertical-align: middle;
  font-size: 22px;
}
</style>
