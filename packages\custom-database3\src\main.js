import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import ViewUIPlus from 'view-ui-plus'
import Particles from '@tsparticles/vue3'
import { loadSlim } from '@tsparticles/slim'
import { VueMasonryPlugin } from 'vue-masonry'
import tools from './utils/tools'
import './assets/css/scrollbar.css'
import 'view-ui-plus/dist/styles/viewuiplus.css'
import 'normalize.css/normalize.css'
import filesystem from 'file-system'
import { LoadCertificateCommunication } from './api/index'
import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css'
import ContextMenu from '@imengyu/vue3-context-menu'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
const app = createApp(App)
app.use(ElementPlus)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.use(createPinia()).use(router).use(ViewUIPlus).use(ContextMenu)
app.use(VueMasonryPlugin)
app.use(Particles, {
  init: async (engine) => {
    await loadSlim(engine)
  }
})
app.use(filesystem)
app.provide('$tools', tools)
// 引入加密狗并建立通信
LoadCertificateCommunication(app)
app.mount('#app')
