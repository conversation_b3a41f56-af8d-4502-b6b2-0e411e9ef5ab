<template>
  <div class="content" v-if="itemData" ref="itemRefValue">
    <div v-for="(value, key) in itemData" :key="key" class="item_list">
      <p
        class="title"
        :class="dataHaveLastObj.hasOwnProperty(key) ? 'titleClick' : ''"
        @click="dataHaveLastObj.hasOwnProperty(key) ? laseDataClick(key) : null"
      >
        {{ translateKey(key) }}:
      </p>
      <p
        class="item_body item_str"
        :class="dataHaveLastObj.hasOwnProperty(value) ? 'valueClick' : ''"
        @click="dataHaveLastObj.hasOwnProperty(value) ? laseDataClick(value) : null"
        v-if="$tools.isPlainObject(value)"
      >
        {{ key === 'path' ? searchStore.allPathDesc[value] : value }}
      </p>
      <p v-else-if="Object.prototype.toString.call(value) === '[object Array]'" class="item_body">
        [
        <span class="item_str" v-for="(str, index) in value" :key="str"
          >"{{ str }}"<span style="color: black" v-if="index !== value.length - 1">,</span>
        </span>
        ]
      </p>
      <Image
        v-else-if="value.sha512_hash"
        :src="`/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/${value.file_type}/${value.sha512_hash}`"
        fit="contain"
        class="image"
        preview
        :preview-list="[
          `/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/${value.file_type}/${value.sha512_hash}`
        ]"
      />
      <VueJsonPretty
        class="item_body"
        v-else
        :data="value"
        :showLength="true"
        :showIcon="true"
        :renderNodeKey="renderKeys"
      />
      <!-- :collapsedNodeLength="2" -->
    </div>
  </div>
</template>

<script setup>
import { ref, inject, onMounted, h } from 'vue'
import enTozh from '@/utils/zhMap'
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
import { useSearchStore } from '@/stores/search'
const $tools = inject('$tools')
const searchStore = useSearchStore()
const dataHaveLastObj = ref(searchStore.dataHaveLastObj)
const props = defineProps({
  itemData: {
    type: Object,
    default: () => ({})
  }
})
const itemData = ref(props.itemData)
const itemRefValue = ref(null)
onMounted(() => {
  itemRefValue.value.style.height = itemRefValue.value.offsetHeight + 'px'
})
const translateKey = (key) => {
  let keyStr = key.replace(/^['"]|['"]$/g, '').split(';')[0]
  return enTozh(keyStr)
}
// 自定义渲染键
const renderKeys = ({ node, defaultKey }) => {
  let keyStr = defaultKey.replace(/^['"]|['"]$/g, '').split(';')[0]
  return enTozh(keyStr)
}

const emit = defineEmits(['last-data-click'])
// 向父组件传递事件
const laseDataClick = (lastKey) => {
  emit('last-data-click', lastKey)
}
</script>

<style lang="scss" scoped>
.content {
  overflow: auto;
  .item_list {
    display: flex;
    .title {
      width: 25%;
      font-weight: bold;
    }
    .titleClick {
      color: rgb(67, 67, 235);
      text-decoration: underline;
      cursor: pointer;
    }
    .item_body {
      width: 75%;
    }
    .item_str {
      color: #13ce66;
      word-break: break-all;
    }
    .image {
      width: 50px;
      height: 50px;
    }
    .valueClick {
      color: rgb(67, 67, 235);
      text-decoration: underline;
      cursor: pointer;
    }
  }
}

:deep(.vjs-tree-brackets) {
  color: #1f1fff;
}
</style>
