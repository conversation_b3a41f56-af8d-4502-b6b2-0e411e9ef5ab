{"name": "custom-database3", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@imengyu/vue3-context-menu": "^1.4.1", "@logicflow/core": "^1.2.28", "@logicflow/extension": "^1.2.28", "@tsparticles/slim": "^3.4.0", "@tsparticles/vue3": "^3.0.1", "axios": "^1.8.1", "certificate-login": "workspace:*", "crypto-js": "^4.2.0", "dc-websocket-jsonrpc": "workspace:*", "file-system": "workspace:*", "normalize.css": "^8.0.1", "pinia": "^2.1.7", "sha.js": "^2.4.11", "tsparticles-slim": "^2.12.0", "view-ui-plus": "^1.3.17", "vue": "^3.4.29", "vue-cookies": "^1.8.4", "vue-json-pretty": "^2.4.0", "vue-masonry": "^0.16.0", "vue-router": "^4.3.3"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "prettier": "^3.2.5", "sass": "^1.77.6", "vite": "^5.3.1", "vite-plugin-static-copy": "^3.1.1"}}