<template>
  <div style="position: relative" class="search_view">
    <Spin size="large" fix :show="searchStore.pathTreeLoad"></Spin>
    <Spin size="large" fix :show="searchStore.searchPrefixLoad || searchStore.searchLastLoad">
      <Icon type="ios-loading" size="20" class="demo-spin-icon-load"></Icon>
      <div>
        前缀搜索路径：{{ searchStore.searchData.pathArr[searchStore.prefixSearchPathIndex] }}
      </div>
      <div>内容搜索路径：{{ searchStore.searchData.pathArr[searchStore.dataSearchPathIndex] }}</div>
      <Button
        v-show="showCancelBtn"
        type="success"
        style="margin-top: 10px"
        @click="cancelSearch"
        title=""
      >
        取消搜索
      </Button>
    </Spin>
    <!-- 搜索头部 -->
    <div class="search_header">
      <div class="search_input">
        <div class="search_data_type">
          <b style="margin-right: 10px">数据类型:</b>
          <el-tree-select
            ref="selectPathTreeRef"
            v-model="selectPathValue"
            :data="searchStore.allPathTreeData"
            placeholder="请选择路径, 默认搜索全部路径"
            style="width: 350px"
            multiple
            clearable
            check-strictly
            default-expand-all
            show-checkbox
            collapse-tags
            collapse-tags-tooltip
            node-key="value"
            value-key="value"
            :props="dataTypeProps"
            @check="dataTypeChange"
          />
        </div>
        <div class="input_btn">
          <Input
            enter-button="搜索"
            search
            clearable
            v-model="searchValue"
            placeholder="请输入查询关键字"
            @on-search="searchClick()"
            @on-focus="changeShowHisDom(true)"
            @on-blur="changeShowHisDom(false)"
          />
          <div
              class="search_his"
              v-if="hisValueData.length && showHisDom"
              @mouseenter="handleMouseEnter"
              @mouseleave="handleMouseLeave"
            >
              <div class="his_list">
                <List>
                  <ListItem
                    class="his_item"
                    v-for="item in hisValueData"
                    @click="searchHisValue(item)"
                    :key="item"
                  >
                    {{ item }}
                  </ListItem>
                </List>
              </div>
              <Button 
                style="float: right" 
                type="text" 
                icon="md-close" 
                @click="clearHisSearchArr"
              >
                清空历史搜索记录
              </Button>
            </div>
        </div>
      </div>
      <div class="print_box">
        <Icon
          type="ios-print-outline"
          class="print_icon"
          size="26"
          @click="printCurrentPage"
        ></Icon>
      </div>
    </div>
    <!-- 搜索内容 -->
    <div class="search_body">
      <div class="search_result">
        <div class="prefix_search">
          <div class="prefix_title">
            <b>结果分类</b>
          </div>
          <div
            class="prefix_list"
            @scroll="prefixListScroll"
            v-if="searchStore.combinedSearchList.length"
          >
            <Card
              class="prefix_card"
              v-for="item in searchStore.combinedSearchList"
              :key="item.row"
              @click="preciseCardClick(item)"
              :class="{ 'active-card': item.row === activeCardRow }"
            >
              <p :title="item.columnValues.title">
                <b>数据类型：</b>{{ searchStore.allPathDesc[item.columnValues.path] }}
              </p>
              <p :title="item.columnValues.title">
                <b>命中字段：</b>
                <span v-html="highlightKeyword(item.columnValues.title, searchValue)"></span>
              </p>
            </Card>
            <p class="search_over_tip" v-if="searchStore.isSearchCombined">数据已经到底了!</p>
          </div>
          <div class="no_prefix" v-else>
            <p>未查询到前缀数据。</p>
          </div>
        </div>
        <div class="data_search">
          <div class="data_title">
            <div class="data_title_left">
              <b style="margin-top: 5px; margin-right: 10px">数据详情</b>
              <!-- 全选复选框 -->

              <Button
                v-if="searchStore.dataSearchListShow.length"
                size="small"
                @click="handleSelectAll"
                type="primary"
                >{{ isAllSelected ? '取消全选' : '全选' }}</Button
              >
              <div v-show="selectedItems.length > 0">
                <Button style="margin-left: 10px" type="primary" @click="exportToExcel" size="small"
                  >导出Excel</Button
                >
                <Button style="margin-left: 10px" size="small" @click="toCollect" type="primary"
                  >收藏</Button
                >
                <Button style="margin-left: 10px" size="small" @click="delDatasSome" type="primary"
                  >删除</Button
                >
              </div>
            </div>
            <div class="data_title_right" v-if="searchStore.pathShow">
              <div class="search_tags">
                <el-tag
                  v-for="(tag, index) in searchTags"
                  :key="index"
                  closable
                  @close="handleTagClose(index)"
                >
                  {{ tag }}
                </el-tag>
              </div>
              <Input
                enter-button="内容搜索"
                search
                clearable
                v-model="searchChildValue"
                placeholder="请输入查询关键字"
                @on-search="addSearchTag(searchChildValue)"
              />
            </div>
          </div>
          <!-- <div
            class="data_list"
            v-if="searchStore.dataSearchListShow.length"
            @scroll="contentListScroll"
          > -->
          <div class="data_list" v-if="searchStore.dataSearchListShow.length">
            <div class="twitterlistRow" v-for="(item, index) in searchStore.dataSearchListShow">
              <div class="twitterlistRowLeft">
                <label>
                  <input
                    style="cursor: pointer"
                    type="checkbox"
                    :value="item"
                    v-model="selectedItems"
                    @change="handleSingleSelect(item)"
                  />
                </label>
              </div>

              <div class="twitterlistRowMid">
                <div v-for="(value, key) in item" :key="key" class="twitterlistRowMidcon">
                  <div class="twitterlistRowMidconTit">
                    <b>{{ key }}</b>
                  </div>

                  <div class="twitterlistRowMidconVal" :title="value">
                    <span v-html="highlightKeyword(value.toString(), searchValue)"></span>
                  </div>
                </div>
              </div>
              <div class="twitterlistRowMidBtn">
                <Button size="small" style="margin-top: 10px" @click="editFn(item)">编辑</Button>
                <Button size="small" style="margin-top: 10px" @click="toCopy(item)">复制</Button>
                <Button size="small" @click="toCollectSingBtn(item)" style="margin-top: 10px"
                  >收藏</Button
                >
                <Button size="small" @click="delDatas(item)" style="margin-top: 10px">删除</Button>
              </div>
            </div>

            <!-- <div v-masonry item-selector=".item" horizontal-order="true" class="data_item">
              <div
                v-masonry-tile
                v-for="(item, index) in searchStore.dataSearchList"
                :key="index"
                class="item"
              >
                <template v-if="item">
                  <Content :itemData="item" @last-data-click="laseDataClick"></Content>
                </template>
              </div>
            </div> -->
            <p
              class="search_over_tip"
              v-if="
                searchStore.dataSearchOver &&
                Math.ceil(searchStore.dataSearchList.length / 20) === nowpage
              "
            >
              数据已经到底了!
            </p>
          </div>
          <div
            class="no_data"
            v-if="searchStore.dataSearchListShow.length == 0 && searchStore.pathShow.length > 0"
          >
            <p>未查询到数据。</p>
          </div>
          <div
            class="no_data"
            v-if="searchStore.dataSearchListShow.length == 0 && searchStore.pathShow.length == 0"
          >
            <p>请点击左侧菜单获取数据</p>
          </div>
          <div style="text-align: center">
            <Page
              :total="searchStore.dataSearchList.length"
              size="small"
              :page-size="20"
              @on-change="pageChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <el-dialog
    v-model="collectDialog"
    title="选取目录"
    width="800"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div style="width: 95%">
      <cellectTree :listType="'username'" :tableName="'favorites_data'" @getCollect="getCollect" />
    </div>
  </el-dialog>
  <el-dialog
    v-model="editDialog"
    title="编辑"
    width="800"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div style="width: 95%">
      <el-form :model="editForm" label-width="auto" style="max-width: 600px">
        <el-form-item v-for="(value, key) in editForm" :key="key" :label="key">
          <el-input
            v-model="editForm[key]"
            :disabled="key === 'row' || key === 'path' ? true : false"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">保存</el-button>
          <el-button @click="editDialog = false">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<script setup>
import { useClipboard } from '@vueuse/core'
const { text, copy: copyToClipboard } = useClipboard()
import { useLoginStore } from '@/stores/login'
const loginStore = useLoginStore()
import { ElDialog, ElMessageBox, ElMessage } from 'element-plus'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import { storeToRefs } from 'pinia'
import { ref, onMounted, nextTick, onBeforeUnmount, inject, watch } from 'vue'
import { useSearchStore } from '@/stores/search'
import { Modal, Message, Icon } from 'view-ui-plus'
import cellectTree from '@/components/collectTree.vue'
import Content from '@/components/ContentView.vue'
import { sliderEmits } from 'element-plus'
import { data } from 'autoprefixer'

const editForm = ref({})
const nowpage = ref(1)
const searchTags = ref([])
// 高亮处理函数
const highlightKeyword = (text, keyword) => {
  // 前置判断：如果关键词为空，直接返回原文本
  if (!keyword || !text) {
    return text
  }

  const regex = new RegExp(keyword, 'gi')

  return text.replace(regex, (match) => {
    return `<span class="highlight-text">${match}</span>`
  })
}
const $tools = inject('$tools')
const searchStore = useSearchStore()
const hisValueData = ref(JSON.parse(localStorage.getItem('hisValueData')) || ['测试搜索1', '测试搜索2'])

const dataTypeProps = {
  children: 'children',
  label: 'title'
}
const isAllSelected = ref(false)
const searchChildValue = ref('')
const selectedItems = ref([])
const selectedItemsSing = ref([])
const activeCardRow = ref(null)
const selectDatabaseValue = ref('公共权限')
const currentDatabaseValue = ref('公共权限')
const selectCaseNameValue = ref([])
const currentCaseNameValue = ref([])
const collectDialog = ref(false)
const collectNum = ref(0)
// 选择搜索条件
const firstSearchObj = ref({
  searchValue: '',
  selectDatabaseValue: '',
  selectPathValue: []
})
const {
  searchValue,
  selectPathValue,
  dataSearchList,
  dataSearchListShow,
  setNowsearchDataPath,
  setNowsearchDataRelation,
  editDialog,
  searchData
} = storeToRefs(searchStore)
const currentPathValue = ref([])
const selectPathTreeRef = ref(null)

// 路径选择变化
const dataTypeChange = (data, checkedStatus) => {
  let node = selectPathTreeRef.value.getNode(data)
  let isChecked = node.checked
  if (data.children && isChecked) {
    selectChildren(data, true)
  } else if (data.children && !isChecked) {
    selectChildren(data, false)
  }
}

const selectChildren = (node, checked) => {
  node.children.forEach((child) => {
    console.log('selectChildren:', child)

    if (child.children) {
      selectChildren(child, checked)
    }
    if (checked) {
      selectPathValue.value.push(child.value)
    } else {
      const index = selectPathValue.value.indexOf(child.value)
      if (index > -1) {
        selectPathValue.value.splice(index, 1)
      }
    }
  })
}

//方法
//二次查询
const addSearchTag = (v) => {
  nowpage.value = 1
  searchTags.value.push(v)
  searchStore.clearDataSearchList()
  searchStore.fetchSearchData(nowpage.value, searchTags.value)
  searchChildValue.value = ''
}
const handleTagClose = (index) => {
  searchTags.value.splice(index, 1)
  nowpage.value = 1
  searchStore.clearDataSearchList()
  searchStore.fetchSearchData(nowpage.value, searchTags.value)
}
//复制
const toCopy = (v) => {
  copyToClipboard(JSON.stringify(v, null, 2))
  ElMessage({
    type: 'success',
    message: '复制成功'
  })
}
//点击页码
const pageChange = (v) => {
  nowpage.value = v
  isAllSelected.value = false
  selectedItems.value = []
  console.log(v, dataSearchListShow.value)
  if (v < 5) {
    dataSearchListShow.value = searchStore.dataSearchList.slice((v - 1) * 20, v * 20)
  } else {
    if (searchStore.dataSearchList.length / 20 == v) {
      dataSearchListShow.value = searchStore.dataSearchList.slice((v - 1) * 20, v * 20)

      searchStore.fetchSearchData(v, searchTags.value)
    } else {
      dataSearchListShow.value = searchStore.dataSearchList.slice((v - 1) * 20, v * 20)
    }
  }
  nextTick(() => {
    const dataListEl = document.querySelector('.data_list')
    if (dataListEl) {
      dataListEl.scrollTop = 0
    }
  })
}
//使用当前时间戳与随机数结合当作预警词的唯一id
const reduceNumber = () => {
  let soleValue = Math.round(new Date().getTime() / 1000).toString()
  let random = new Array('a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n')
  for (let i = 0; i < 6; i++) {
    let index = Math.floor(Math.random() * 13)
    soleValue += random[index]
  }
  return soleValue
}
const getCollect = (data) => {
  const { $data } = useLoginStore().wsMethod
  console.log('collect', data)
  ElMessageBox.confirm('确定收藏到此目录?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      let a = []
      if (selectedItemsSing.value.length > 0) {
        a = selectedItemsSing.value
      } else {
        a = selectedItems.value
      }
      a.forEach((element) => {
        let prefix = 1e13 - Math.round(new Date().getTime() / 1000) + data.id + reduceNumber()
        $data.sendData(
          'Api.Search.SearchPrefixTable.AddData',
          [
            {
              msg: {
                type: 'username',
                authority: loginStore.userinfo.authority,
                username: loginStore.userinfo.username,
                table: 'favorites_data',
                prefix,
                relation: data.id + ';searchCollect',
                data: {
                  data: {
                    file_data: element
                  }
                }
              }
            }
          ],
          (res) => {
            console.log('收藏', res)
            if (res.status === 'ok') {
              collectNum.value++
              if (collectNum.value === a.length) {
                ElMessage({
                  type: 'success',
                  message: '收藏成功'
                })
                collectNum.value = 0

                /*  selectedItems.value = [] */
                collectDialog.value = false
              }
            }
          }
        )
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消'
      })
    })
}

// 新增：跳转过来之后，页面路径处理
const handlePathChange = () => {
  const selectPathData = localStorage.getItem('selectPathData')
  if (selectPathData) {
    searchStore.allPathTreeData = JSON.parse(selectPathData)
    searchStore.pathTreeLoad = false
  }
}

// 页面初始化
const initialize = () => {
  hisValueData.value = JSON.parse(localStorage.getItem('hisValueData')) || []
  searchStore.caseTreeLoad = true
  searchStore.pathTreeLoad = true
  // 获取路径树数据
  const selectPathData = localStorage.getItem('selectPathData')
  searchStore.clearPathTreeData()
  if (selectPathData) {
    searchStore.allPathTreeData = JSON.parse(selectPathData)
    searchStore.pathTreeLoad = false
  } else {
    searchStore.getPathTreeData()
  }
  const allPathArr = localStorage.getItem('allPathArr')
  const allPathDesc = localStorage.getItem('allPathDesc')
  if (allPathArr) {
    searchStore.allPathArr = JSON.parse(allPathArr)
  }
  if (allPathDesc) {
    searchStore.allPathDesc = JSON.parse(allPathDesc)
  }
  // 获取案件数据
  const selectCaseData = localStorage.getItem('selectCaseData')
  searchStore.clearCaseTreeData()
  if (selectCaseData) {
    searchStore.selectCaseData = JSON.parse(selectCaseData)
    searchStore.caseTreeLoad = false
  } else {
    searchStore.getCaseTreeData()
  }
}
// 刷新案件
const refreshCase = () => {
  searchStore.caseTreeLoad = true
  searchStore.clearCaseTreeData()
  searchStore.getCaseTreeData()
}
// 刷新路径
const refreshPath = () => {
  searchStore.pathTreeLoad = true
  searchStore.clearPathTreeData()
  searchStore.getPathTreeData()
}

onBeforeUnmount(() => {
  if (popstateTimeout) {
    clearTimeout(popstateTimeout)
    popstateTimeout = null
  }
})
// 统计路径下的数据
const statisticPathNum = () => {
  searchStore.sendStatisticsPath()
}
// 选择数据库
const changeRadioValue = () => {
  changClearHistoricalPath()
  selectCaseNameValue.value = []
  currentCaseNameValue.value = []
}
const changeCaseNameValue = () => {
  changClearHistoricalPath()
}

// 选择路径变化 - 自定义父子节点联动实现
const selectChangePath = (selectedValues) => {
  console.log('selectChangePath', selectedValues)
  // const treeSelectComponent = selectPathTreeRef.value
  // if (treeSelectComponent) {
  //   const checkedNodes = treeSelectComponent.getCheckedNodes(false)
  //   const allSelectedValues = checkedNodes.map((node) => node.value)
  //   searchStore.selectPathValue = allSelectedValues
  // }

  // 如果没有传入参数，说明是组件内部调用，直接执行清理逻辑
  // if (!selectedValues) {
  //   changClearHistoricalPath()
  //   return
  // }
  // // 使用 nextTick 确保在 DOM 更新后获取正确的值
  // nextTick(() => {
  //   // 如果 selectPathValue.value 有值但 store 中没有，则手动同步
  //   if (selectPathValue.value && selectPathValue.value.length > 0 &&
  //       (!searchStore.selectPathValue || searchStore.selectPathValue.length === 0)) {
  //     searchStore.selectPathValue = [...selectPathValue.value]
  //   }
  // })

  // // 原有的清除历史路径逻辑
  // // changClearHistoricalPath()

  if (Array.isArray(selectedValues) && selectedValues.length === 0) {
    console.log('检测到清空操作，直接更新 store。')
    // 直接用这个空数组去更新 store，这是最直接、最正确的做法。
    searchStore.selectPathValue = []
    return // 处理完毕，直接返回，不再执行后续逻辑
  }

  // 2. 如果不是清空操作（即用户在勾选/取消勾选节点），则执行我们之前的逻辑
  //    来获取包含所有父节点的完整列表。
  const treeSelectComponent = selectPathTreeRef.value
  if (treeSelectComponent) {
    // 注意：即使在这里，我们也可以考虑优先使用 currentSelectedValues，
    // 但为了保留“获取所有父节点”的功能，我们继续使用 getCheckedNodes。
    const checkedNodes = treeSelectComponent.getCheckedNodes(false)
    const allSelectedValues = checkedNodes.map((node) => node.value)

    console.log('用户选择了节点，更新 store 为全量路径:', allSelectedValues)
    searchStore.selectPathValue = allSelectedValues
  }
}

// 初始化时设置初始选择状态
onMounted(() => {
  initialize()
  searchClick()
})

// 展开路径树
const unfoldPathTree = (bool) => {
  if (bool) {
    searchStore.setPathDataNum()
  }
}
// 切换搜索条件时需要清空搜索路径
const changClearHistoricalPath = () => {
  if (historicalPathArr.value.length > 1) {
    Modal.confirm({
      title: '提示',
      content: '执行当前操作会清空搜索路径,是否继续执行?',
      onOk: () => {
        clearHistoricalPath()
        currentDatabaseValue.value = selectDatabaseValue.value
        currentCaseNameValue.value = selectCaseNameValue.value
        currentPathValue.value = selectPathValue.value
      },
      onCancel: () => {
        selectDatabaseValue.value = currentDatabaseValue.value
        selectCaseNameValue.value = currentCaseNameValue.value
        selectPathValue.value = currentPathValue.value
      }
    })
  } else {
    currentDatabaseValue.value = selectDatabaseValue.value
    currentCaseNameValue.value = selectCaseNameValue.value
    currentPathValue.value = selectPathValue.value
  }
}

let showCancelBtn = ref(false)
const cancelSearch = () => {
  searchStore.searchPrefixLoad = false
  searchStore.searchLastLoad = false
}
const setLoadingTimeout = () => {
  showCancelBtn.value = false
  setTimeout(() => {
    console.log('<setLoadingTimeout> 超时')
    if (searchStore.searchPrefixLoad || searchStore.searchLastLoad) {
      console.log('<setLoadingTimeout> 设置参数')
      showCancelBtn.value = true
    }
  }, 15000)
}

// 搜索
// const searchValue = ref('')
const isShowResult = ref(false)
const searchClick = (type) => {
  /** 新增判断： 是否为用户手动触发的顶层搜索， 如果是， 就重置下钻路径， 确保这是一个全新的搜索 */
  if (type !== 'router' && historicalPathArr.value.length > 1) {
    console.log('historicalPathArr:', historicalPathArr)
    historicalPathArr.value = ['/']
    nowCheckPath.value = '/'
  }

  activeCardRow.value = null
  searchStore.clearDataSearchList()
  searchStore.clearData()
  setLoadingTimeout()
  setHisSearchValue()
  let database = ''
  if (!selectCaseNameValue.value.length && selectDatabaseValue.value === '案件权限') {
    Message.error({
      content: '请选择案件!',
      duration: 0,
      closable: true
    })
    return
  }
  const caseId = selectCaseNameValue.value[selectCaseNameValue.value.length - 1]
  switch (selectDatabaseValue.value) {
    case '公共权限':
      database = 'public'
      break
    case '部门权限':
      database = 'authority'
      break
    case '用户权限':
      database = 'username'
      break
    case '案件权限':
      database = 'case'
      break
  }
  const searchData = {
    value: searchValue.value,
    database: database,
    caseId: caseId,
    pathArr: selectPathValue.value,
    relationArr: historicalPathArr.value
  }

  selectedItems.value = []
  //新增： 调用新的合并搜索函数
  searchStore.setSearchData(searchData)
  // 只调用新的主控函数来启动搜索流程
  searchStore.startInitialSearch()
  // 调用新的内容搜索函数
  /*  searchStore.fetchSearchData() */

  // searchStore.setPrefixSearchData(searchData)
  // searchStore.searchPrefixLoad = true // 新增： 由 action 内部控制
  //searchStore.searchLastLoad = true
  searchStore.prefixSeachOver = false
  searchStore.dataSearchOver = false

  // // 前缀搜索
  // searchStore.getPrefixSearchRelation()
  // // 精确搜索
  // searchStore.getPreciseSearchRelation()
  // 内容搜索
  // searchStore.getSearchData()
  isShowResult.value = true
  // 记录第一次搜的条件
  if (nowCheckPath.value === '/') {
    firstSearchObj.value.searchValue = searchValue.value
    firstSearchObj.value.selectDatabaseValue = selectDatabaseValue.value
    firstSearchObj.value.selectPathValue = selectPathValue.value
  }
  if (!type) {
    const query = JSON.stringify({
      searchValue: searchValue.value,
      selectDatabaseValue: selectDatabaseValue.value,
      selectCaseNameValue: selectCaseNameValue.value,
      selectPathValue: selectPathValue.value,
      historicalPathArr: historicalPathArr.value
    })
    history.pushState({ data: query }, '')
  }
}
//全选
const handleSelectAll = () => {
  isAllSelected.value = !isAllSelected.value
  console.log('isAllSelected', isAllSelected.value)
  if (isAllSelected.value) {
    selectedItems.value = searchStore.dataSearchListShow.map((item) => ({ ...item }))
    console.log('quanx', selectedItems.value)
    // 创建新数组确保响应式更新
  } else {
    selectedItems.value = []
  }
}
const handleSingleSelect = (data) => {
  /* this.$forceUpdate() */
  if (selectedItems.value.length === searchStore.dataSearchListShow.length) {
    isAllSelected.value = true
  } else {
    isAllSelected.value = false
  }
  console.log('单个选择', selectedItems.value)
}

//编辑
const editFn = (v) => {
  editForm.value = JSON.parse(JSON.stringify(v))
  searchStore.editDialog = true
}
const onSubmit = () => {
  searchStore.editDataFn(editForm.value, nowpage.value)
}
//导出
const exportToExcel = () => {
  if (selectedItems.value.length === 0) {
    ElMessage({
      type: 'warning',
      message: '请选择要导出的数据'
    })
    return
  }

  const worksheetData = []

  selectedItems.value.forEach((item) => {
    const keys = Object.keys(item)
    const values = Object.values(item).map((val) =>
      val !== null && val !== undefined ? String(val) : ''
    )

    worksheetData.push(keys)
    worksheetData.push(values)
    worksheetData.push([]) // 添加一个空行用于间隔
  })

  const ws = XLSX.utils.aoa_to_sheet(worksheetData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

  const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
  function formatDateTime(input, separator = '-') {
    const date = input instanceof Date ? input : new Date(input)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return [year, month, day].join(separator) + ` ${hours}:${minutes}`
  }
  let fileName = (searchValue.value || 'export') + '(' + formatDateTime(new Date()) + ')' + '.xlsx'
  saveAs(new Blob([wbout], { type: 'application/octet-stream' }), fileName)
}
/* const exportToExcel = () => {
  const excelList = selectedItems.value
  if (!excelList || excelList.length === 0) {
    ElMessage.warning('请选择要导出的数据')
    return
  }

  const dataForSheet = {}
  let maxRow = 0

  // 创建表头行
  const headerRow = ['']
  excelList.forEach((_, colIndex) => {
    headerRow.push(`数据${colIndex + 1}`)
  })
  dataForSheet[0] = headerRow
  maxRow = 1

  // 填充数据行
  excelList.forEach((item, colIndex) => {
    let rowIndex = 1
    for (const key in item) {
      if (Object.prototype.hasOwnProperty.call(item, key)) {
        // 键的行
        if (!dataForSheet[rowIndex]) dataForSheet[rowIndex] = []
        dataForSheet[rowIndex][colIndex + 1] = key
        rowIndex++

        // 值的行
        if (!dataForSheet[rowIndex]) dataForSheet[rowIndex] = []
        dataForSheet[rowIndex][colIndex + 1] = item[key]
        rowIndex++
        //空一行
        if (!dataForSheet[rowIndex]) dataForSheet[rowIndex] = []
        dataForSheet[rowIndex][colIndex + 1] = ''
        rowIndex++
      }
    }
    if (rowIndex > maxRow) {
      maxRow = rowIndex
    }
  })

  // 将数据对象转换为工作表所需的数组格式 (aoa)
  const excelDataArr = []
  const numCols = excelList.length + 1
  for (let i = 0; i < maxRow; i++) {
    const sourceRow = dataForSheet[i] || []
    const targetRow = []
    for (let j = 0; j < numCols; j++) {
      const cellValue = sourceRow[j]
      targetRow.push(cellValue === undefined || cellValue === null ? '' : cellValue)
    }

    excelDataArr.push(targetRow)
  }

  // 将数据转换为工作表
  const worksheet = XLSX.utils.aoa_to_sheet(excelDataArr)

  // 设置列宽
  worksheet['!cols'] = headerRow.map(() => ({ wpx: 150 }))

  // 创建工作簿并添加工作表
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

  // 生成Excel文件
  const excelBuffer = XLSX.write(workbook, {
    bookType: 'xlsx',
    type: 'array'
  })

  // 使用blob和FileReader创建一个Blob URL
  const dataBlob = new Blob([excelBuffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
  })
  const blobUrl = window.URL.createObjectURL(dataBlob)
  function formatDateTime(input, separator = '-') {
    const date = input instanceof Date ? input : new Date(input)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return [year, month, day].join(separator) + ` ${hours}:${minutes}`
  }
  let fileName = (searchValue.value || 'export') + '(' + formatDateTime(new Date()) + ')' + '.xlsx'
  // 使用saveAs下载文件
  saveAs(dataBlob, fileName)

  // 清理
  window.URL.revokeObjectURL(blobUrl)
} */
//收藏
const toCollectSingBtn = (v) => {
  selectedItemsSing.value = [v]
  collectDialog.value = true
}
const toCollect = () => {
  console.log('shouc', collectDialog.value)
  selectedItemsSing.value = []
  collectDialog.value = true
}
//删除
const delDatasSome = () => {
  ElMessageBox.confirm('确定要删除?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      isAllSelected.value = false

      try {
        const result = searchStore.delDataFn(selectedItems.value)
        selectedItems.value = []
      } catch (error) {
        console.error('Error in delDataFn call:', error)
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消'
      })
    })
}
const delDatas = (v) => {
  ElMessageBox.confirm('确定要删除?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      isAllSelected.value = false
      selectedItems.value = []
      selectedItems.value.push(v)
      try {
        const result = searchStore.delDataFn(selectedItems.value)
        selectedItems.value = []
      } catch (error) {
        console.error('Error in delDataFn call:', error)
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消'
      })
    })
}
let popstateTimeout = null
window.addEventListener('popstate', (event) => {
  if (popstateTimeout) {
    clearTimeout(popstateTimeout) // 清除之前的定时器
  }
  popstateTimeout = setTimeout(() => {
    if (event?.state?.data) {
      const hisStateData = JSON.parse(event.state.data)
      searchValue.value = hisStateData.searchValue
      selectDatabaseValue.value = hisStateData.selectDatabaseValue
      selectCaseNameValue.value = hisStateData.selectCaseNameValue
      selectPathValue.value = []
      selectPathValue.value.push(...hisStateData.selectPathValue)
      historicalPathArr.value = hisStateData.historicalPathArr
      nowCheckPath.value = hisStateData.historicalPathArr[hisStateData.historicalPathArr.length - 1]
      searchClick('router')
    }
    popstateTimeout = null // 重置定时器
  }, 300) // 300ms 的节流时间
})

/** 新增： 合并搜索的滚动事件 */
const prefixListScroll = (event) => {
  // 使用新的完成状态和加载状态
  if (searchStore.isSearchCombined || searchStore.searchPrefixLoad) {
    return
  }
  const element = event.target
  const scrollTop = element.scrollTop
  const clientHeight = element.clientHeight
  const scrollHeight = element.scrollHeight
  if (scrollTop + clientHeight >= scrollHeight - 10) {
    // 新增： 调用新的合并搜索函数进行分页
    searchStore.fetchMorePrefixResults()
  }
}

// 内容搜索的滚动事件
/* const contentListScroll = (event) => {
  if (searchStore.dataSearchOver) {
    return
  }
  const element = event.target
  const scrollTop = element.scrollTop
  const clientHeight = element.clientHeight
  const scrollHeight = element.scrollHeight
  if (scrollTop + clientHeight >= scrollHeight) {
    searchStore.searchLastLoad = true
    searchStore.getSearchData()
  }
} */
// 点击内容的下一次搜索
const laseDataClick = (lastKey) => {
  historicalPathArr.value.push(lastKey)
  nowCheckPath.value = searchStore.dataHaveLastObj[lastKey]
  searchClick()
}
// 点击精确匹配类型搜索
const preciseCardClick = (item) => {
  isAllSelected.value = false
  selectedItems.value = []
  searchTags.value = []
  searchChildValue.value = ''
  console.log('item', item)
  searchStore.clearDataSearchList()
  const key = item.columnValues.r.type.name
  const data = item.columnValues

  activeCardRow.value = item.row

  // 新增： 调用新的数据内容搜索函数
  // 1. 准备下钻所需的信息
  const drillDownInfo = {
    path: data.path, // 数据的原始路径
    relation: `${data.title};${key}` // 拼接下钻关系，例如："姓名;张三"
  }
  searchStore.setNowsearchDataPath = data.path
  searchStore.setNowsearchDataRelation = `${data.title};${key}`
  searchStore.fetchSearchData(1, searchTags.value)
  // searchStore.fetchSearchData(drillDownInfo)
  // 2. 更新历史路径，这对于UI展示和可能的返回操作仍然有用
  if (!historicalPathArr.value.includes(data.title)) {
    historicalPathArr.value.push(data.title)
    historicalPathArr.value.push(key)
    nowCheckPath.value = key
  }

  // 3. 清空主搜索框的值
  /* searchValue.value = '' */

  // 4. 显示加载状态
  searchStore.searchLastLoad = true

  // 5. 直接调用 getSearchData 并传入下钻信息
  //searchStore.fetchSearchData(drillDownInfo)
}

// 搜索路径
const nowCheckPath = ref('/')
const historicalPathArr = ref(['/'])
const historicalPathObj = ref({ '/': {} })
const historicalPathClick = (path) => {
  nowCheckPath.value = path
  const index = historicalPathArr.value.indexOf(path) + 1
  historicalPathArr.value.splice(index)
  if (path === '/') {
    searchValue.value = firstSearchObj.value.searchValue
    selectDatabaseValue.value = firstSearchObj.value.selectDatabaseValue
    selectPathValue.value = []
    nextTick(() => {
      selectPathValue.value = [...firstSearchObj.value.selectPathValue]
      searchClick()
    })
  } else {
    searchClick()
  }
}
// 添加搜索路径
const showAddHisPath = ref(false)
const addhisPathValue = ref('')
const inputHisPathRef = ref(null)
const addHistoricalPath = () => {
  showAddHisPath.value = true
  nextTick(() => {
    inputHisPathRef.value.focus()
  })
}
const addHisPath = () => {
  if (addhisPathValue.value !== '') {
    historicalPathArr.value.push(addhisPathValue.value)
    historicalPathObj.value[addhisPathValue.value] = {}
    nowCheckPath.value = addhisPathValue.value
    Message.success({
      background: true,
      content: '添加搜索路径成功'
    })
    showAddHisPath.value = false
    addhisPathValue.value = ''
  } else {
    showAddHisPath.value = false
  }
}
// 清除搜索路径
const clearHistoricalPath = () => {
  nowCheckPath.value = '/'
  historicalPathArr.value = ['/']
  historicalPathObj.value = { '/': {} }
  selectPathValue.value = []
  isShowResult.value = false
}

// 搜索历史记录
const showHisDom = ref(false)
const isMouseOver = ref(false)
const isFoucs = ref(false)
const handleMouseEnter = () => {
  isMouseOver.value = true
}
const handleMouseLeave = () => {
  isMouseOver.value = false
  if (!isFoucs.value) {
    showHisDom.value = false
  }
}
const changeShowHisDom = (bool) => {
  isFoucs.value = bool
  if (!isMouseOver.value) {
    showHisDom.value = bool
  }
}
const setHisSearchValue = () => {
  if (searchValue.value) {
    if (hisValueData.value.includes(searchValue.value)) {
      const index = hisValueData.value.indexOf(searchValue.value)
      hisValueData.value.splice(index, 1)
    }
    hisValueData.value.unshift(searchValue.value)
    if (hisValueData.value.length > 10) {
      hisValueData.value.pop()
    }
    localStorage.setItem('hisValueData', JSON.stringify(hisValueData.value))
  }
}
window.addEventListener('storage', (event) => {
  if (event.key === 'hisValueData') {
    hisValueData.value = JSON.parse(localStorage.getItem('hisValueData')) || []
  }
})
const searchHisValue = (item) => {
  searchValue.value = item
  searchClick()
}
const clearHisSearchArr = () => {
  hisValueData.value = []
  localStorage.setItem('hisValueData', JSON.stringify(hisValueData.value))
}
const printCurrentPage = () => {
  window.print()
}
</script>

<style scoped lang="scss">
.search_view {
  height: 90vh;
  width: 100%;
  background-color: #fff;
}
.search_header {
  height: 5%;
  width: 100%;
  margin-top: 10px;
  background-color: #fff;
  padding: 10px 10px 0px;
  display: flex;
}
.search_input {
  width: 90%;
  display: flex;
  .search_data_type {
    width: auto;
    display: flex;
    align-items: center;
    margin-left: 5px;
    .tree_select {
      width: 250px;
    }
  }
  .input_btn {
    margin-left: 20px;
    width: 30%;
    position: relative;
    z-index: 10000;
    .search_his {
  position: fixed !important;
  z-index: 999999 !important;
  background: red !important;
  border: 3px solid blue !important;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  overflow-y: auto;
  width: 300px !important;
  top: 200px !important;
  left: 200px !important;
  
  .his_list {
    padding: 4px 0;
    
    .his_item {
      padding: 8px 12px;
      cursor: pointer;
      
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}
  }
}

.print_box {
  width: 10%;
  height: 32px;
  text-align: right;

  .print_icon {
    cursor: pointer;
    &:hover {
      color: #2d8cf0;
    }
  }
}

.his_path {
  margin: 10px 0 0 10px;
  height: 36px;
  display: flex;
  align-items: center;
  .path_list {
    margin-left: 5px;
    width: 96%;
    display: flex;
    align-items: center;
    overflow-x: auto;
    white-space: nowrap;
    .path_item {
      cursor: pointer;
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .active_path {
      border: 1px solid #118d11 !important;
    }
    .path_item:hover {
      border: 1px solid #2d8cf0 !important;
    }
  }
}
.search_body {
  width: 100%;
  height: 95%;
  overflow: auto;
  padding: 10px 10px 0px;
  .search_criteria {
    height: 120px;
    background-color: #fff;
    .search_database {
      padding: 0px 20px;
      height: 60px;
      border-bottom: 1px dashed #adadad;
      display: flex;
      align-items: center;
      .radio_group {
        margin: 0 10px;
      }
    }
    .search_path {
      padding: 0px 20px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .data_num {
        display: flex;
        p {
          font-weight: bold;
          margin-left: 20px;
          font-size: 15px;
          span {
            color: #2b85e4;
            padding: 0 5px;
            font-size: 19px;
          }
        }
      }
    }
  }
  :deep(.highlight-text) {
    color: #ff0000;
    font-weight: bold;
    background-color: transparent;
  }
  .search_result {
    display: flex;
    height: 100%;
    .prefix_search {
      width: 15%;
      height: 100%;
      position: relative;
      .prefix_title {
        padding: 2px;
      }
      .prefix_list {
        position: relative;
        overflow: auto;
        height: 95%;
        .prefix_card {
          background-color: #fff;
          margin-bottom: 5px;
          cursor: pointer;
          :deep(.ivu-card-body) {
            padding: 5px;
          }
          :deep(.highlight-text) {
            color: #ff0000;
            font-weight: bold;
            background-color: transparent;
          }
        }
        .prefix_card:hover {
          background-color: #e9e9e9;
        }
        .active-card {
          border: 1px solid #2d8cf0;
          background-color: #f0faff;
          box-shadow: 0 0 5px rgba(45, 140, 240, 0.5);
        }
      }
      .no_prefix {
        width: 100%;
        height: 95%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .prefix_type_tip {
        position: absolute;
        top: 25px;
        left: 95%;
        border: 1px solid rgba(0, 0, 0, 0.1);
        background-color: #fff;
        width: 150%;
        .precise_card {
          border-radius: 2px;
          border-bottom: 1px solid rgba(0, 0, 0, 0.2);
          cursor: pointer;
          .card_body {
            display: flex;
            align-items: center;
            padding: 5px;
            .precise_img {
              width: 45px;
              margin-right: 5px;
            }
            .precise_type {
              p {
                width: 170px;
                display: -webkit-box;
                overflow: hidden;
                white-space: pre-wrap;
                -webkit-line-clamp: 3;
                line-clamp: 3;
                -webkit-box-orient: vertical;
              }
            }
          }
        }
        .precise_card:hover {
          background-color: #cccccc;
        }
      }
    }

    .precise_search {
      width: 15%;
      height: 60vh;
      margin-right: 10px;
      .precise_title {
        padding: 2px;
      }
      .precise_list {
        height: 95%;
        overflow: auto;
        .precise_card {
          margin-bottom: 5px;
          background-color: #fff;
          border-radius: 2px;
          cursor: pointer;
          .card_body {
            display: flex;
            align-items: center;
            padding: 5px;
            .precise_img {
              width: 45px;
              margin-right: 5px;
            }
            .precise_type {
              p {
                width: 170px;
                display: -webkit-box;
                overflow: hidden;
                white-space: pre-wrap;
                -webkit-line-clamp: 3;
                line-clamp: 3;
                -webkit-box-orient: vertical;
              }
            }
          }
        }
      }
      .no_precise {
        width: 100%;
        height: 95%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .data_search {
      width: 85%;
      height: 100%;
      .data_title {
        padding: 2px;
        display: flex;
        justify-content: space-between;
        .data_title_left {
          display: flex;
        }
      }
      .data_list {
        height: 90%;
        overflow: auto;
        .data_item {
          width: 100%;
        }
        .item {
          overflow: auto;
          width: 33%;
          margin: 0 4px 4px 0;
          border: 1px solid #e9e7e7;
          background-color: #fff;
          border-radius: 5px;
          padding: 10px;
          box-shadow: 0px 2px 10px 1px rgba(0, 0, 0, 0.1);
        }
        .twitterlistRow {
          display: flex;
          border-radius: 10px; /* 圆角大小 */
          box-shadow: 0px 2px 10px 1px rgba(0, 0, 0, 0.1);
          padding-bottom: 10px;
          padding-top: 10px;
          margin: 10px;
          .twitterlistRowLeft {
            text-align: center;
            min-width: 30px;
          }
          .twitterlistRowMid {
            display: flex;
            flex-wrap: wrap;
            .twitterlistRowMidcon {
              padding: 10px;
              .twitterlistRowMidconTit {
                min-width: 150px;
                text-align: center;
              }
              .twitterlistRowMidconVal {
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 150px; /* 或固定宽度 */
                display: inline-block;
              }
            }
          }
          .twitterlistRowMidBtn {
            padding-right: 5px;
            text-align: right;

            min-width: 200px;
            flex-wrap: wrap;
          }
        }
      }
      .no_data {
        width: 100%;
        height: 95%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .search_over_tip {
      color: #2b85e4;
      text-align: center;
      background-color: #fff;
      padding: 10px 0;
    }
  }
}
:deep(.ivu-select-dropdown) {
  max-height: 300px;
}
:deep(.ivu-select-default.ivu-select-multiple .ivu-select-selection) {
  max-height: 32px;
  overflow: hidden;
}

/* 设置 Input 组件高度为 32px */
:deep(.ivu-input-wrapper .ivu-input-search) {
  height: 32px;
}

:deep(.ivu-input-wrapper .ivu-input-search .ivu-input-group-append .ivu-input-search-enter-button) {
  height: 32px;
}

:deep(.ivu-input) {
  height: 32px;
  line-height: 32px;
}

:deep(.ivu-input-search-enter-button) {
  height: 32px;
}

:deep(.ivu-input-search-enter-button .ivu-input) {
  height: 32px;
  line-height: 32px;
}

:deep(.ivu-input-search-enter-button .ivu-btn) {
  height: 32px;
  line-height: 30px;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
.data_title_right {
  display: flex;
  align-items: center;
}

.search_tags {
  margin-left: 10px;
  display: flex;
  gap: 5px;
}
</style>
