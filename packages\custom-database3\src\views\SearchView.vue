<template>
  <div style="position: relative" class="search_view">
    <Spin size="large" fix :show="searchStore.caseTreeLoad || searchStore.pathTreeLoad"></Spin>
    <Spin size="large" fix :show="searchStore.searchPrefixLoad || searchStore.searchLastLoad">
      <Icon type="ios-loading" size="20" class="demo-spin-icon-load"></Icon>
      <div>
        前缀搜索路径：{{ searchStore.searchData.pathArr[searchStore.prefixSearchPathIndex] }}
      </div>
      <div>内容搜索路径：{{ searchStore.searchData.pathArr[searchStore.dataSearchPathIndex] }}</div>
      <Button
        v-show="showCancelBtn"
        type="success"
        style="margin-top: 10px"
        @click="cancelSearch"
        title=""
      >
        取消搜索
      </Button>
    </Spin>
    <!-- 搜索头部 -->
    <div class="search_header">
      <div class="search_input">
        <div class="search_data_type">
          <b style="margin-right: 10px;">数据类型:</b>
          <TreeSelect
              v-model="selectPathValue"
              placeholder="请选择路径, 默认搜索全部路径"
              multiple
              show-checkbox
              :check-strictly="false"
              :data="searchStore.allPathTreeData"
              v-width="350"
              @on-change="selectChangePath"
              @on-open-change="unfoldPathTree"
            />
        </div>
        <div class="input_btn">
          <Input
            enter-button="搜索"
            search
            clearable
            v-model="searchValue"
            placeholder="请输入查询关键字"
            @on-search="searchClick()"
            @on-focus="changeShowHisDom(true)"
            @on-blur="changeShowHisDom(false)"
          />
        </div>
      </div>
      <div class="print_box">
        <Icon type="ios-print-outline" size="26" @click="printCurrentPage"></Icon>
      </div>
    </div>
    <!-- 搜索内容 -->
    <div class="search_body">
      <div class="search_result">
        <div class="prefix_search">
          <div class="prefix_title">
            <b>结果分类</b>
          </div>
          <div
            class="prefix_list"
            @scroll="prefixListScroll"
            v-if="searchStore.combinedSearchList.length"
          >
            <Card
              class="prefix_card"
              v-for="item in searchStore.combinedSearchList"
              :key="item.row"
              @click="preciseCardClick(item)"
              :class="{ 'active-card': item.row === activeCardRow }"
            >
              <p :title="item.columnValues.title">
                <b>数据类型：</b>{{ searchStore.allPathDesc[item.columnValues.path] }}
              </p>
              <p :title="item.columnValues.title">
                <b>命中字段：</b><span class="card_text">“{{ item.columnValues.title }}”</span>
              </p>
            </Card>
            <p class="search_over_tip" v-if="searchStore.isSearchCombined">数据已经到底了!</p>
          </div>
          <div class="no_prefix" v-else>
            <p>未查询到前缀数据。</p>
          </div>
        </div>
        <div class="data_search">
          <div class="data_title"><b>数据详情</b></div>
          <div
            class="data_list"
            v-if="searchStore.dataSearchList.length"
            @scroll="contentListScroll"
          >
            <div v-masonry item-selector=".item" horizontal-order="true" class="data_item">
              <div
                v-masonry-tile
                v-for="(item, index) in searchStore.dataSearchList"
                :key="index"
                class="item"
              >
                <template v-if="item">
                  <Content :itemData="item" @last-data-click="laseDataClick"></Content>
                </template>
              </div>
            </div>
            <p class="search_over_tip" v-if="searchStore.dataSearchOver">数据已经到底了!</p>
          </div>
          <div class="no_data" v-else>
            <p>未查询到数据。</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { ref, onMounted, nextTick, onBeforeUnmount, inject } from 'vue'
import { useSearchStore } from '@/stores/search'
import { Modal, Message, Icon } from 'view-ui-plus'
import Content from '@/components/ContentView.vue'
const $tools = inject('$tools')
const searchStore = useSearchStore()
const hisValueData = ref(JSON.parse(localStorage.getItem('hisValueData')) || [])

const activeCardRow = ref(null)
const selectDatabaseValue = ref('公共权限')
const currentDatabaseValue = ref('公共权限')
const selectCaseNameValue = ref([])
const currentCaseNameValue = ref([])
// 选择搜索条件
const firstSearchObj = ref({
  searchValue: '',
  selectDatabaseValue: '',
  selectPathValue: []
})
const { searchValue, selectPathValue } = storeToRefs(searchStore)
const currentPathValue = ref([])

onMounted(() => {
  initialize()
})

// 页面初始化
const initialize = () => {
  hisValueData.value = JSON.parse(localStorage.getItem('hisValueData')) || []
  searchStore.caseTreeLoad = true
  searchStore.pathTreeLoad = true
  // 获取路径树数据
  const selectPathData = localStorage.getItem('selectPathData')
  searchStore.clearPathTreeData()
  if (selectPathData) {
    searchStore.allPathTreeData = JSON.parse(selectPathData)
    searchStore.pathTreeLoad = false
  } else {
    searchStore.getPathTreeData()
  }
  const allPathArr = localStorage.getItem('allPathArr')
  const allPathDesc = localStorage.getItem('allPathDesc')
  if (allPathArr) {
    searchStore.allPathArr = JSON.parse(allPathArr)
  }
  if (allPathDesc) {
    searchStore.allPathDesc = JSON.parse(allPathDesc)
  }
  // 获取案件数据
  const selectCaseData = localStorage.getItem('selectCaseData')
  searchStore.clearCaseTreeData()
  if (selectCaseData) {
    searchStore.selectCaseData = JSON.parse(selectCaseData)
    searchStore.caseTreeLoad = false
  } else {
    searchStore.getCaseTreeData()
  }
}
// 刷新案件
const refreshCase = () => {
  searchStore.caseTreeLoad = true
  searchStore.clearCaseTreeData()
  searchStore.getCaseTreeData()
}
// 刷新路径
const refreshPath = () => {
  searchStore.pathTreeLoad = true
  searchStore.clearPathTreeData()
  searchStore.getPathTreeData()
}

onBeforeUnmount(() => {
  if (popstateTimeout) {
    clearTimeout(popstateTimeout)
    popstateTimeout = null
  }
})
// 统计路径下的数据
const statisticPathNum = () => {
  searchStore.sendStatisticsPath()
}
// 选择数据库
const changeRadioValue = () => {
  changClearHistoricalPath()
  selectCaseNameValue.value = []
  currentCaseNameValue.value = []
}
const changeCaseNameValue = () => {
  changClearHistoricalPath()
}

const selectChangePath = (selectedValues) => {
  console.log("<selectChangePath> 接收到的选择值:", selectedValues);

  // 直接更新选择值，因为已经设置了 check-strictly="false"
  // TreeSelect 组件会自动处理父子节点联动
  selectPathValue.value = selectedValues;

  console.log("<selectChangePath> 更新后的选择值:", selectPathValue.value);

  // 原有的清除历史路径逻辑
  changClearHistoricalPath();
};

// 选择路径变化
// const selectChangePath = () => {
  
//   changClearHistoricalPath()
// }
// 展开路径树
const unfoldPathTree = (bool) => {
  if (bool) {
    searchStore.setPathDataNum()
  }
}
// 切换搜索条件时需要清空搜索路径
const changClearHistoricalPath = () => {
  if (historicalPathArr.value.length > 1) {
    Modal.confirm({
      title: '提示',
      content: '执行当前操作会清空搜索路径,是否继续执行?',
      onOk: () => {
        clearHistoricalPath()
        currentDatabaseValue.value = selectDatabaseValue.value
        currentCaseNameValue.value = selectCaseNameValue.value
        currentPathValue.value = selectPathValue.value
      },
      onCancel: () => {
        selectDatabaseValue.value = currentDatabaseValue.value
        selectCaseNameValue.value = currentCaseNameValue.value
        selectPathValue.value = currentPathValue.value
      }
    })
  } else {
    currentDatabaseValue.value = selectDatabaseValue.value
    currentCaseNameValue.value = selectCaseNameValue.value
    currentPathValue.value = selectPathValue.value
  }
}

let showCancelBtn = ref(false)
const cancelSearch = ()=>{
  searchStore.searchPrefixLoad = false
  searchStore.searchLastLoad = false
}
const setLoadingTimeout = ()=>{
  showCancelBtn.value = false
  setTimeout(()=>{
    console.log("<setLoadingTimeout> 超时");
    if (searchStore.searchPrefixLoad || searchStore.searchLastLoad){
      console.log("<setLoadingTimeout> 设置参数");
      showCancelBtn.value = true
    }
  }, 15000)
}

// 搜索
// const searchValue = ref('')
const isShowResult = ref(false)
const searchClick = (type) => {
  /** 新增判断： 是否为用户手动触发的顶层搜索， 如果是， 就重置下钻路径， 确保这是一个全新的搜索 */
  if (type !== 'router' && historicalPathArr.value.length > 1) {
    console.log("historicalPathArr:", historicalPathArr);
    historicalPathArr.value = ['/']
    nowCheckPath.value = '/'
  }
 
  activeCardRow.value = null

  setLoadingTimeout()
  setHisSearchValue()
  let database = ''
  if (!selectCaseNameValue.value.length && selectDatabaseValue.value === '案件权限') {
    Message.error({
      content: '请选择案件!',
      duration: 0,
      closable: true
    })
    return
  }
  const caseId = selectCaseNameValue.value[selectCaseNameValue.value.length - 1]
  switch (selectDatabaseValue.value) {
    case '公共权限':
      database = 'public'
      break
    case '部门权限':
      database = 'authority'
      break
    case '用户权限':
      database = 'username'
      break
    case '案件权限':
      database = 'case'
      break
  }
  const searchData = {
    value: searchValue.value,
    database: database,
    caseId: caseId,
    pathArr: selectPathValue.value,
    relationArr: historicalPathArr.value
  }

  //新增： 调用新的合并搜索函数
  searchStore.setSearchData(searchData)
  // 只调用新的主控函数来启动搜索流程
  searchStore.startInitialSearch()
  // 调用新的内容搜索函数
  searchStore.fetchSearchData()

  // searchStore.setPrefixSearchData(searchData)
  // searchStore.searchPrefixLoad = true // 新增： 由 action 内部控制
  searchStore.searchLastLoad = true
  searchStore.prefixSeachOver = false
  searchStore.dataSearchOver = false

  // // 前缀搜索
  // searchStore.getPrefixSearchRelation()
  // // 精确搜索
  // searchStore.getPreciseSearchRelation()
  // 内容搜索
  // searchStore.getSearchData()
  isShowResult.value = true
  // 记录第一次搜的条件
  if (nowCheckPath.value === '/') {
    firstSearchObj.value.searchValue = searchValue.value
    firstSearchObj.value.selectDatabaseValue = selectDatabaseValue.value
    firstSearchObj.value.selectPathValue = selectPathValue.value
  }
  if (!type) {
    const query = JSON.stringify({
      searchValue: searchValue.value,
      selectDatabaseValue: selectDatabaseValue.value,
      selectCaseNameValue: selectCaseNameValue.value,
      selectPathValue: selectPathValue.value,
      historicalPathArr: historicalPathArr.value
    })
    history.pushState({ data: query }, '')
  }
}
let popstateTimeout = null
window.addEventListener('popstate', (event) => {
  if (popstateTimeout) {
    clearTimeout(popstateTimeout) // 清除之前的定时器
  }
  popstateTimeout = setTimeout(() => {
    if (event?.state?.data) {
      const hisStateData = JSON.parse(event.state.data)
      searchValue.value = hisStateData.searchValue
      selectDatabaseValue.value = hisStateData.selectDatabaseValue
      selectCaseNameValue.value = hisStateData.selectCaseNameValue
      selectPathValue.value = []
      selectPathValue.value.push(...hisStateData.selectPathValue)
      historicalPathArr.value = hisStateData.historicalPathArr
      nowCheckPath.value = hisStateData.historicalPathArr[hisStateData.historicalPathArr.length - 1]
      searchClick('router')
    }
    popstateTimeout = null // 重置定时器
  }, 300) // 300ms 的节流时间
})


/** 新增： 合并搜索的滚动事件 */
const prefixListScroll = (event) => {
  // 使用新的完成状态和加载状态
  if (searchStore.isSearchCombined || searchStore.searchPrefixLoad) {
    return
  }
  const element = event.target
  const scrollTop = element.scrollTop
  const clientHeight = element.clientHeight
  const scrollHeight = element.scrollHeight
  if (scrollTop + clientHeight >= scrollHeight - 10) {
    // 新增： 调用新的合并搜索函数进行分页
    searchStore.fetchMorePrefixResults()
  }
}

// 内容搜索的滚动事件
const contentListScroll = (event) => {
  if (searchStore.dataSearchOver) {
    return
  }
  const element = event.target
  const scrollTop = element.scrollTop
  const clientHeight = element.clientHeight
  const scrollHeight = element.scrollHeight
  if (scrollTop + clientHeight >= scrollHeight) {
    searchStore.searchLastLoad = true
    searchStore.getSearchData()
  }
}
// 点击内容的下一次搜索
const laseDataClick = (lastKey) => {
  historicalPathArr.value.push(lastKey)
  nowCheckPath.value = searchStore.dataHaveLastObj[lastKey]
  searchClick()
}
// 点击精确匹配类型搜索
const preciseCardClick = (item) => {
  const key = item.columnValues.r.type.name
  const data = item.columnValues

  activeCardRow.value = item.row

  // 新增： 调用新的数据内容搜索函数
  // 1. 准备下钻所需的信息
  const drillDownInfo = {
    path: data.path, // 数据的原始路径
    relation: `${data.title};${key}` // 拼接下钻关系，例如："姓名;张三"
  };

  // 2. 更新历史路径，这对于UI展示和可能的返回操作仍然有用
  if (!historicalPathArr.value.includes(data.title)) {
    historicalPathArr.value.push(data.title);
    historicalPathArr.value.push(key);
    nowCheckPath.value = key;
  }
  
  // 3. 清空主搜索框的值
  searchValue.value = '';

  // 4. 显示加载状态
  searchStore.searchLastLoad = true;
  
  // 5. 直接调用 getSearchData 并传入下钻信息
  searchStore.fetchSearchData(drillDownInfo);

}

// 搜索路径
const nowCheckPath = ref('/')
const historicalPathArr = ref(['/'])
const historicalPathObj = ref({ '/': {} })
const historicalPathClick = (path) => {
  nowCheckPath.value = path
  const index = historicalPathArr.value.indexOf(path) + 1
  historicalPathArr.value.splice(index)
  if (path === '/') {
    searchValue.value = firstSearchObj.value.searchValue
    selectDatabaseValue.value = firstSearchObj.value.selectDatabaseValue
    selectPathValue.value = []
    nextTick(() => {
      selectPathValue.value = [...firstSearchObj.value.selectPathValue]
      searchClick()
    })
  } else {
    searchClick()
  }
}
// 添加搜索路径
const showAddHisPath = ref(false)
const addhisPathValue = ref('')
const inputHisPathRef = ref(null)
const addHistoricalPath = () => {
  showAddHisPath.value = true
  nextTick(() => {
    inputHisPathRef.value.focus()
  })
}
const addHisPath = () => {
  if (addhisPathValue.value !== '') {
    historicalPathArr.value.push(addhisPathValue.value)
    historicalPathObj.value[addhisPathValue.value] = {}
    nowCheckPath.value = addhisPathValue.value
    Message.success({
      background: true,
      content: '添加搜索路径成功'
    })
    showAddHisPath.value = false
    addhisPathValue.value = ''
  } else {
    showAddHisPath.value = false
  }
}
// 清除搜索路径
const clearHistoricalPath = () => {
  nowCheckPath.value = '/'
  historicalPathArr.value = ['/']
  historicalPathObj.value = { '/': {} }
  selectPathValue.value = []
  isShowResult.value = false
}

// 搜索历史记录
const showHisDom = ref(false)
const isMouseOver = ref(false)
const isFoucs = ref(false)
const handleMouseEnter = () => {
  isMouseOver.value = true
}
const handleMouseLeave = () => {
  isMouseOver.value = false
  if (!isFoucs.value) {
    showHisDom.value = false
  }
}
const changeShowHisDom = (bool) => {
  isFoucs.value = bool
  if (!isMouseOver.value) {
    showHisDom.value = bool
  }
}
const setHisSearchValue = () => {
  if (searchValue.value) {
    if (hisValueData.value.includes(searchValue.value)) {
      const index = hisValueData.value.indexOf(searchValue.value)
      hisValueData.value.splice(index, 1)
    }
    hisValueData.value.unshift(searchValue.value)
    if (hisValueData.value.length > 10) {
      hisValueData.value.pop()
    }
    localStorage.setItem('hisValueData', JSON.stringify(hisValueData.value))
  }
}
window.addEventListener('storage', (event) => {
  if (event.key === 'hisValueData') {
    hisValueData.value = JSON.parse(localStorage.getItem('hisValueData')) || []
  }
})
const searchHisValue = (item) => {
  searchValue.value = item
  searchClick()
}
const clearHisSearchArr = () => {
  hisValueData.value = []
  localStorage.setItem('hisValueData', JSON.stringify(hisValueData.value))
}
const printCurrentPage = () => {
  window.print()
}
</script>

<style scoped lang="scss">
.search_view {
  height: 90vh;
  width: 100%;
  background-color: #fff;
}
.search_header {
  height: 5%;
  width: 100%;
  margin-top: 10px;
  background-color: #fff;
  padding: 10px 10px 0px;
  display: flex;
}
.search_input {
  width: 90%;
  display: flex;
  .search_data_type {
    width: auto;
    display: flex;
    align-items: center;
    margin-left: 5px;
    .tree_select {
      width: 250px;
    }
  }
  .input_btn {
    margin-left: 20px;
    width: 30%;
    position: relative;
  }
}

.print_box {
  width: 10%;
  height: 32px;
  text-align: right;
  &:hover {
    cursor: pointer;
    color: #2d8cf0;
  }
}

.his_path {
  margin: 10px 0 0 10px;
  height: 36px;
  display: flex;
  align-items: center;
  .path_list {
    margin-left: 5px;
    width: 96%;
    display: flex;
    align-items: center;
    overflow-x: auto;
    white-space: nowrap;
    .path_item {
      cursor: pointer;
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .active_path {
      border: 1px solid #118d11 !important;
    }
    .path_item:hover {
      border: 1px solid #2d8cf0 !important;
    }
  }
}
.search_body {
  width: 100%;
  height: 95%;
  overflow: auto;
  padding: 10px 10px 0px;
  .search_criteria {
    height: 120px;
    background-color: #fff;
    .search_database {
      padding: 0px 20px;
      height: 60px;
      border-bottom: 1px dashed #adadad;
      display: flex;
      align-items: center;
      .radio_group {
        margin: 0 10px;
      }
    }
    .search_path {
      padding: 0px 20px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .data_num {
        display: flex;
        p {
          font-weight: bold;
          margin-left: 20px;
          font-size: 15px;
          span {
            color: #2b85e4;
            padding: 0 5px;
            font-size: 19px;
          }
        }
      }
    }
  }
  .search_result {
    display: flex;
    height: 100%;
    .prefix_search {
      width: 15%;
      height: 100%;
      position: relative;
      .prefix_title {
        padding: 2px;
      }
      .prefix_list {
        position: relative;
        overflow: auto;
        height: 95%;
        .prefix_card {
          background-color: #fff;
          margin-bottom: 5px;
          cursor: pointer;
          :deep(.ivu-card-body) {
            padding: 5px;
          }
          .card_text {
            color:red;
            display: -webkit-box;
            overflow: hidden;
            white-space: pre-wrap;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
          }
        }
        .prefix_card:hover {
          background-color: #e9e9e9;
        }
        .active-card {
          border: 1px solid #2d8cf0;
          background-color: #f0faff;
          box-shadow: 0 0 5px rgba(45, 140, 240, 0.5);
        }
      }
      .no_prefix {
        width: 100%;
        height: 95%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .prefix_type_tip {
        position: absolute;
        top: 25px;
        left: 95%;
        border: 1px solid rgba(0, 0, 0, 0.1);
        background-color: #fff;
        width: 150%;
        .precise_card {
          border-radius: 2px;
          border-bottom: 1px solid rgba(0, 0, 0, 0.2);
          cursor: pointer;
          .card_body {
            display: flex;
            align-items: center;
            padding: 5px;
            .precise_img {
              width: 45px;
              margin-right: 5px;
            }
            .precise_type {
              p {
                width: 170px;
                display: -webkit-box;
                overflow: hidden;
                white-space: pre-wrap;
                -webkit-line-clamp: 3;
                line-clamp: 3;
                -webkit-box-orient: vertical;
              }
            }
          }
        }
        .precise_card:hover {
          background-color: #cccccc;
        }
      }
    }

    .precise_search {
      width: 15%;
      height: 60vh;
      margin-right: 10px;
      .precise_title {
        padding: 2px;
      }
      .precise_list {
        height: 95%;
        overflow: auto;
        .precise_card {
          margin-bottom: 5px;
          background-color: #fff;
          border-radius: 2px;
          cursor: pointer;
          .card_body {
            display: flex;
            align-items: center;
            padding: 5px;
            .precise_img {
              width: 45px;
              margin-right: 5px;
            }
            .precise_type {
              p {
                width: 170px;
                display: -webkit-box;
                overflow: hidden;
                white-space: pre-wrap;
                -webkit-line-clamp: 3;
                line-clamp: 3;
                -webkit-box-orient: vertical;
              }
            }
          }
        }
      }
      .no_precise {
        width: 100%;
        height: 95%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .data_search {
      width: 85%;
      height: 100%;
      .data_title {
        padding: 2px;
      }
      .data_list {
        height: 95%;
        overflow: auto;
        .data_item {
          width: 100%;
        }
        .item {
          overflow: auto;
          width: 33%;
          margin: 0 4px 4px 0;
          border: 1px solid #e9e7e7;
          background-color: #fff;
          border-radius: 5px;
          padding: 10px;
          box-shadow: 0px 2px 10px 1px rgba(0, 0, 0, 0.1);
        }
      }
      .no_data {
        width: 100%;
        height: 95%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .search_over_tip {
      color: #2b85e4;
      text-align: center;
      background-color: #fff;
      padding: 10px 0;
    }
  }
}
:deep(.ivu-select-dropdown) {
  max-height: 300px;
}
:deep(.ivu-select-default.ivu-select-multiple .ivu-select-selection) {
  max-height: 32px;
  overflow: hidden;
}

/* 设置 Input 组件高度为 32px */
:deep(.ivu-input-wrapper .ivu-input-search) {
  height: 32px;
}

:deep(.ivu-input-wrapper .ivu-input-search .ivu-input-group-append .ivu-input-search-enter-button) {
  height: 32px;
}

:deep(.ivu-input) {
  height: 32px;
  line-height: 32px;
}

:deep(.ivu-input-search-enter-button) {
  height: 32px;
}

:deep(.ivu-input-search-enter-button .ivu-input) {
  height: 32px;
  line-height: 32px;
}

:deep(.ivu-input-search-enter-button .ivu-btn) {
  height: 32px;
  line-height: 30px;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
</style>
