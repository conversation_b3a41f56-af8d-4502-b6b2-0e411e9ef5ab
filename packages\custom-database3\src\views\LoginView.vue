<template>
  <div class="login_bg">
    <div class="login_title">
      <img src="@/assets/images/logo.jpg" />
      <p>智网社情分析系统</p>
    </div>
    <p class="cert_wait_text">
      证书验证中，请等待...<Icon type="ios-loading" class="ivu-anim-loop" size="24" />
    </p>
    <div class="particle-container">
      <vue-particles id="tsparticles" :options="particlesOptions" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useLoginStore } from '@/stores/login'
import { useRouter } from 'vue-router'

const particlesOptions = ref({
  fpsLimit: 120,
  interactivity: {
    events: {
      onClick: {
        enable: true,
        mode: 'push'
      },
      onHover: {
        enable: true,
        mode: 'repulse'
      }
    },
    modes: {
      bubble: {
        distance: 400,
        duration: 2,
        opacity: 0.8,
        size: 40
      },
      push: {
        quantity: 4
      },
      repulse: {
        distance: 200,
        duration: 0.4
      }
    }
  },
  particles: {
    color: {
      value: '#ffffff'
    },
    links: {
      color: '#ffffff',
      distance: 150,
      enable: true,
      opacity: 0.5,
      width: 1
    },
    move: {
      direction: 'none',
      enable: true,
      outModes: 'bounce',
      random: false,
      speed: 4,
      straight: false
    },
    number: {
      density: {
        enable: true
      },
      value: 80
    },
    opacity: {
      value: 0.5
    },
    shape: {
      type: 'circle'
    },
    size: {
      value: { min: 1, max: 5 }
    }
  },
  detectRetina: true
})
onMounted(() => {
  goHome()
})
const login = useLoginStore()
const router = useRouter()
const goHome = () => {
  if (login.userinfo?.username) {
    router.push('/index')
  }
}
</script>

<style scoped lang="scss">
.login_bg {
  width: 100%;
  height: 100vh;
  background: url('@/assets/images/login.jpeg') no-repeat center top / cover;
  position: relative;
  .login_title {
    position: absolute;
    display: flex;
    align-items: center;
    top: 20px;
    left: 20px;
    img {
      width: 50px;
      height: 50px;
      border-radius: 10px;
    }
    p {
      margin-left: 20px;
      font-size: 24px;
      font-family: 'Courier New', Courier, monospace;
      color: #fff;
    }
  }
  .cert_wait_text {
    font-size: 24px;
    font-family: 'Courier New', Courier, monospace;
    color: #fff;
    position: absolute;
    bottom: 20px;
    right: 20px;
  }
  .particle-container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
}
</style>
