import { defineStore } from 'pinia'
import { Message } from 'view-ui-plus'
import router from '@/router'
import VueCookies from 'vue-cookies'

export const useLoginStore = defineStore('loginStore', {
  state: () => ({
    // pinia中的ws请求全局状态管理
    wsMethod: {},
    // 数据信息
    restrictedPopover: 0,
    userinfo: {},
    nowPath: '',
    reconnectTime: null,
    errorTime: null,
    closeTime: null
  }),
  actions: {
    // 存储路由状态
    setRouterPath(path) {
      this.nowPath = path
    },
    // 获取用户信息
    getUserInfo() {
      const { $pki } = this.wsMethod
      $pki.sendData('Api.User.Info', [], (res) => {
        this.userinfo = res
        VueCookies.set('authority', this.userinfo.authority, '1d')
        const nowRoutePath = router.options.history.location
        if (nowRoutePath === '/login') {
          router.push('/index')
        }
      })
    },
    socket_on_open() {
      this.restrictedPopover += 1
      if (this.restrictedPopover >= 4) {
        this.restrictedPopover = 0
        if (!this.userinfo.username) {
          this.getUserInfo()
        }
        Message.success({
          background: true,
          content: '通信连接全部成功(Websocket connection succeeded)'
        })
      }
    },
    socket_on_close() {
      clearTimeout(this.closeTime)
      this.closeTime = setTimeout(() => {
        Message.error({
          background: true,
          content: '通信连接已关闭(Websocket connection is closed)'
        })
      }, 1000)
    },
    socket_on_error() {
      clearTimeout(this.errorTime)
      this.errorTime = setTimeout(() => {
        Message.error({
          background: true,
          content: '通信连接错误(Websocket connection is incorrect)'
        })
      }, 1000)
    },
    socket_on_message(message) {
      if (message?.error) {
        Message.error({
          content: message.error,
          duration: 0,
          closable: true
        })
      }
    },
    socket_reconnect(num) {
      clearTimeout(this.reconnectTime)
      this.reconnectTime = setTimeout(() => {
        Message.error({
          background: true,
          content: '通信连接断开,重连第' + num + '次'
        })
      }, 1000)
    },
    socket_reconnect_error() {
      Message.error({
        background: true,
        content: '通信重连失败(Websocket reconnection fails)'
      })
    }
  }
})
