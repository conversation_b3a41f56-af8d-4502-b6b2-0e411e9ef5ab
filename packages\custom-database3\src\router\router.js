import Layout from '@/layout/LayoutView.vue'
const layoutMap = [
	{
    path: '/index',
    name: 'index',
    component: () => import('../views/Index.vue'),
    meta: { title: '数据查询', icon: 'md-search' }
  },
  {
    path: '/search',
    name: 'search',
		hidden: true,
    component: () => import('../views/SearchView.vue'),
    meta: { title: '快速查询', icon: 'md-search' }
  },
  {
    path: '/collect',
    name: 'collect',
    component: () => import('../views/collect.vue'),
    meta: { title: '数据收藏', icon: 'md-folder' }
  },
  {
    path: '/field',
    name: 'field',
    component: () => import('@/views/FieldView.vue'),
    meta: { title: '数据分类', icon: 'md-card' }
  },
  {
    path: '/analysis',
    name: 'analysis',
    component: () => import('@/views/AnalysisView.vue'),
    meta: { title: '数据解析', icon: 'md-analytics' }
  },
  {
    path: '/template',
    name: 'template',
		hidden: true,
    component: () => import('@/views/TemplateView.vue'),
    meta: { title: '模板管理', icon: 'ios-grid' }
  },
	{
    path: '/taskList',
    name: 'taskList',
    component: () => import('@/views/TaskList.vue'),
    meta: { title: '任务队列', icon: 'md-list' }
  },
  {
    path: '/system',
    name: 'system',
    hidden: true,
    component: () => import('@/views/SystemLinkView.vue'),
    meta: { title: '系统列表' }
  }
]
const routers = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'login',
    meta: { title: '登录' },
    component: () => import('@/views/LoginView.vue')
  },
  {
    path: '/404',
    component: () => import('@/views/NotFoundView.vue')
  },
  {
    path: '/',
    meta: { title: '首页' },
    redirect: { name: 'index' },
    component: Layout,
    children: [...layoutMap]
  },
  // 404页面
  { path: '/:pathMatch(.*)*', redirect: '/404' }
]
export { routers, layoutMap }
