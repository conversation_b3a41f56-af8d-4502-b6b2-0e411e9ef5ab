// 时间戳转换(10位)
const timestamp = (time) => {
  // 使用 Date 对象将时间戳转换为日期时间
  let timestamp
  if (String(time).length === 10) {
    timestamp = time * 1000
  } else {
    timestamp = time
  }
  const date = new Date(timestamp)
  // 使用 Date 对象的方法获取年、月、日、时、分、秒
  const year = date.getFullYear() // 年
  const month = ('0' + (date.getMonth() + 1)).slice(-2) // 月份是从 0 开始的，需要加 1，并且保证两位数
  const day = ('0' + date.getDate()).slice(-2) // 日，并且保证两位数
  const hours = ('0' + date.getHours()).slice(-2) // 时，并且保证两位数
  const minutes = ('0' + date.getMinutes()).slice(-2) // 分，并且保证两位数
  const seconds = ('0' + date.getSeconds()).slice(-2) // 秒，并且保证两位数
  const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  return formattedTime
}
// 哈希值转换
import crypto from 'crypto-js'
const sha512 = (data) => {
  return crypto.SHA512(data).toString()
}
const md5 = (data) => {
  return crypto.MD5(data).toString()
}
// 字节转换
const formatBytes = (bytes) => {
  if (bytes === 0) return '0'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  const formattedValue = parseFloat((bytes / Math.pow(k, i)).toFixed(2))
  return `${formattedValue} ${sizes[i]}`
}
// 判断是不是一个字符串或者数字
const isPlainObject = (value) => {
  return (
    Object.prototype.toString.call(value) === '[object Number]' ||
    Object.prototype.toString.call(value) === '[object String]'
  )
}
// 生成随机字符串
const generateRandomString = () => {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  const charsetLength = charset.length

  for (let i = 0; i < 12; i++) {
    const randomIndex = Math.floor(Math.random() * charsetLength)
    result += charset[randomIndex]
  }
  return result
}
// 数据预处理
const dataPreprocessing = (data) => {
  let obj = {
    path: data.columnValues.path
  }
  const d = data.columnValues.d
  for (const key in d) {
    if (Object.hasOwnProperty.call(d, key)) {
      const item = d[key]
      if (Object.prototype.toString.call(item) === '[object Object]') {
        for (const key2 in item) {
          if (Object.hasOwnProperty.call(item, key2)) {
            const value = item[key2]
            if (Object.prototype.toString.call(value) === '[object Object]') {
              obj[key2] = value
            } else {
              if (obj[key2]) {
                obj[key2].push(value)
              } else {
                obj[key2] = [value]
              }
            }
          }
        }
      } else {
        obj[key] = item
      }
    }
  }
  return obj
}
// 数字单位转换
const formatNumber = (num) => {
  // num *= 10
  // if (num < 10000) {
  //   return num.toString() // 不加单位
  // }
  const units = ['万', '千万', '亿']
  const divisors = [10000, 10000000, 100000000]

  for (let i = divisors.length - 1; i >= 0; i--) {
    if (num >= divisors[i]) {
      const formattedNum = num / divisors[i]
      const decimalPart = formattedNum % 1
      return formattedNum.toFixed(decimalPart > 0 ? 2 : 0) + units[i]
    }
  }
}
export default {
  timestamp,
  sha512,
	md5,
  formatBytes,
  isPlainObject,
  generateRandomString,
  dataPreprocessing,
  formatNumber
}
