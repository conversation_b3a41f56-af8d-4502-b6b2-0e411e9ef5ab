<template>
  <div class="template_header">
    <p class="header_text">模板管理</p>
    <div class="change_type">
      <p
        v-for="type in templateList"
        :key="type.name"
        :class="type.name === templateType ? 'change' : ''"
        @click="clickTabGet(type.name)"
      >
        {{ type.label }}
      </p>
    </div>
  </div>
  <div class="template_body">
    <Spin size="large" fix :show="templateStore.showLoading"></Spin>
    <div class="body_left">
      <div
        class="body_item"
        v-for="item in templateStore.templateList"
        :key="item._id"
        @click="changeItem(item)"
        :class="item === defaultItem ? 'item_active' : ''"
      >
        <b v-if="templateType === 'parsing_template'">{{ item._source.title }}</b>
        <b v-if="templateType === 'parsing_data_type'">{{ item._source.nickname }}</b>
        <p>{{ $tools.timestamp(item._source['@timestamp']) }}</p>
      </div>
      <div
        class="body_item"
        @click="changeItem('add_item')"
        :class="'add_item' === defaultItem ? 'item_active' : ''"
      >
        <p><Icon type="md-add" />添加模板</p>
      </div>
    </div>
    <div class="body_right">
      <div class="amend" v-if="templateType === 'parsing_template'">
        <div class="amend_input_title">
          <p>模板名称：</p>
          <Input
            v-model="checkForm.title"
            placeholder="请输入模板名称"
            size="large"
            style="width: 500px"
          />
        </div>
        <div class="amend_input_list">
          <p>字段列表：</p>
          <div class="input_list">
            <Input
              style="width: 500px; margin-bottom: 15px"
              size="large"
              placeholder="请输入字段名称"
              v-for="(item, index) in checkForm.analysis_field"
              :key="index"
              v-model="item.field_name"
            >
              <template #prepend>
                <Checkbox v-model="item.index">允许索引</Checkbox>
                <Checkbox v-model="item.ignore">忽略字段</Checkbox>
              </template>
              <template #append>
                <Button icon="ios-trash" @click="delFileInput(index)"></Button>
              </template>
            </Input>
          </div>
        </div>
        <Button icon="md-add" type="info" @click="addFileInput">添加字段</Button>
      </div>
      <Form
        v-if="templateType === 'parsing_data_type'"
        :model="addRelFrom"
        ref="addRelRef"
        :rules="addRelRules"
        label-position="left"
        :label-width="80"
      >
        <FormItem label="图标：" prop="icon">
          <div>
            <img
              class="addrel_icon"
              v-if="addRelFrom.icon"
              :src="`/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/${addRelFrom.icon}`"
            />
            <Upload
              :show-upload-list="false"
              :on-success="addRelIcon"
              :format="['jpg', 'jpeg', 'png']"
              :max-size="2048"
              :on-format-error="handleFormatError"
              :on-exceeded-size="handleMaxSize"
              :data="{ file_type: 'icon' }"
              name="put_small_file"
              type="drag"
              action="/filesystem/api/rest/v2/node-0/small_file/put_sha512_file"
              class="rel_icon_upload"
            >
              <div class="icon_upload">
                <Icon type="ios-camera" size="20"></Icon>
              </div>
            </Upload>
            <template v-if="templateStore.iconList">
              <img
                class="addrel_icon preselection_icon"
                v-for="item in templateStore.iconList"
                @click="setDataTypeIcon(item)"
                :src="`/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/${item}`"
              />
            </template>
          </div>
        </FormItem>
        <FormItem label="名称：" prop="name">
          <Input v-model="addRelFrom.name" style="width: 500px"></Input>
        </FormItem>
        <FormItem label="昵称：" prop="nickname">
          <Input v-model="addRelFrom.nickname" style="width: 500px"></Input>
        </FormItem>
        <FormItem label="描述：" prop="detail">
          <Input
            type="textarea"
            style="width: 500px"
            :rows="3"
            v-model="addRelFrom.detail"
            class="datail_textarea"
          ></Input>
        </FormItem>
      </Form>
      <div>
        <Button
          type="success"
          icon="md-checkmark-circle-outline"
          size="large"
          style="margin-right: 10px"
          @click="submitItem"
          >{{ defaultItem !== 'add_item' ? '提交修改' : '提交添加' }}</Button
        >
        <Button
          v-if="defaultItem !== 'add_item'"
          type="error"
          icon="ios-trash"
          size="large"
          @click="delItem"
          >删除</Button
        >
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, inject, watch } from 'vue'
import { useTemplateStore } from '@/stores/template'
import { Modal, Message } from 'view-ui-plus'

const $tools = inject('$tools')

const templateStore = useTemplateStore()

const templateList = ref([
  { label: '解析模板', name: 'parsing_template' },
  { label: '数据类型', name: 'parsing_data_type' }
])
const templateType = ref('parsing_template')
const clickTabGet = (name) => {
  templateStore.setTemplateType(name)
  templateStore.getTemplateList()
  templateType.value = name
}
onMounted(() => {
  templateStore.setTemplateType(templateType.value)
  templateStore.getTemplateList()
})
// 操作解析模板
const defaultItem = ref({})
const checkForm = ref({
  title: '',
  analysis_field: []
})
const addRelFrom = ref({
  icon: '',
  name: '',
  nickname: '',
  detail: ''
})
const addRelRules = ref({
  icon: [{ required: true, message: '数据类型图标不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '数据类型名称不能为空', trigger: 'blur' }],
  nickname: [{ required: true, message: '数据类型昵称不能为空', trigger: 'blur' }],
  detail: [{ required: true, message: '数据类型描述不能为空', trigger: 'blur' }]
})
const handleFormatError = () => {
  Message.warning({
    background: true,
    content: '不支持的图片格式，仅支持jpg和png格式'
  })
}
const handleMaxSize = () => {
  Message.warning({
    background: true,
    content: '图片大小不能超过2M'
  })
}
// 设置图标
const setDataTypeIcon = (item) => {
  addRelFrom.value.icon = item
}
const addRelIcon = (res) => {
  addRelFrom.value.icon = res
}
const addFileInput = () => {
  checkForm.value.analysis_field.push({
    field_name: '',
    index: true,
    ignore: false
  })
}
// 删除字段
const delFileInput = (index) => {
  checkForm.value.analysis_field.splice(index, 1)
}
watch(
  () => templateStore.showLoading,
  (newVal) => {
    if (!newVal) {
      if (templateStore?.templateList?.length) {
        defaultItem.value = templateStore?.templateList[0]
        if (templateType.value === 'parsing_template') {
          checkForm.value.title = defaultItem.value._source.title
          checkForm.value.analysis_field = JSON.parse(defaultItem.value._source.analysis_field)
        } else if (templateType.value === 'parsing_data_type') {
          addRelFrom.value.icon = defaultItem.value._source.icon
          addRelFrom.value.name = defaultItem.value._source.name
          addRelFrom.value.nickname = defaultItem.value._source.nickname
          addRelFrom.value.detail = defaultItem.value._source.detail
        }
      } else {
        changeItem('add_item')
      }
    }
  }
)
const changeItem = (item) => {
  if (templateType.value === 'parsing_template') {
    if (item === 'add_item') {
      defaultItem.value = item
      checkForm.value.title = ''
      checkForm.value.analysis_field = [
        {
          field_name: '',
          index: true,
          ignore: false
        }
      ]
    } else {
      defaultItem.value = item
      checkForm.value.title = defaultItem.value._source.title
      checkForm.value.analysis_field = JSON.parse(defaultItem.value._source.analysis_field)
    }
  } else if (templateType.value === 'parsing_data_type') {
    if (item === 'add_item') {
      defaultItem.value = item
      addRelFrom.value = { icon: '', name: '', nickname: '', detail: '' }
    } else {
      defaultItem.value = item
      addRelFrom.value.icon = defaultItem.value._source.icon
      addRelFrom.value.name = defaultItem.value._source.name
      addRelFrom.value.nickname = defaultItem.value._source.nickname
      addRelFrom.value.detail = defaultItem.value._source.detail
    }
  }
}
const submitItem = () => {
  if (templateType.value === 'parsing_template') {
    if (!checkForm.value.title) {
      Message.error({
        background: true,
        content: '模板名称不能为空！'
      })
      return
    }
    let sendMsg = {
      type: 'parsing_data',
      title: checkForm.value.title,
      analysis_field: JSON.stringify(checkForm.value.analysis_field)
    }
    if (defaultItem.value === 'add_item') {
      templateStore.sendAddAnalysisOss({
        id: String(Math.floor(Math.random() * 10000000000)),
        msg: sendMsg
      })
    } else {
      templateStore.updataAnalysis({
        id: defaultItem.value._id,
        msg: sendMsg
      })
    }
  } else if (templateType.value === 'parsing_data_type') {
    let sendMsg = {
      type: 'data_type',
      ...addRelFrom.value
    }
    if (defaultItem.value === 'add_item') {
      templateStore.sendAddAnalysisOss({
        id: String(Math.floor(Math.random() * 10000000000)),
        msg: sendMsg
      })
    } else {
      templateStore.updataAnalysis({
        id: defaultItem.value._id,
        msg: sendMsg
      })
    }
  }
}
const delItem = () => {
  Modal.confirm({
    title: '提示',
    content: '该操作会永久删除该模板，是否确认？',
    onOk: () => {
      templateStore.delAnalysisOss(defaultItem.value._id)
    },
    onCancel: () => {
      Message.info('取消删除！')
    }
  })
}
// 操作数据类型
</script>

<style lang="scss" scoped>
.template_header {
  height: 60px;
  width: 100%;
  margin-top: 10px;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .header_text {
    font-size: 24px;
    padding-left: 10px;
  }
  .change_type {
    padding-right: 40px;
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #000;
    p {
      padding-right: 10px;
    }
    .change {
      font-size: 18px;
      color: rgb(38, 86, 245);
      font-weight: 600;
    }
  }
}
.template_body {
  background-color: #fff;
  margin-top: 10px;
  position: relative;
  height: 84vh;
  display: flex;
  .body_left {
    height: 100%;
    width: 12%;
    border-right: 1px solid #bbb;
    border-top: 1px solid #bbb;
    overflow: auto;
    padding-top: 5px;
    cursor: pointer;
    .body_item {
      padding: 10px;
      color: #000;
      border-bottom: 1px solid #bbb;
      margin: 0 10px;
    }
    .body_item:hover {
      background-color: rgb(159, 185, 241);
    }
    .item_active {
      background-color: rgb(184, 203, 243);
    }
  }
  .body_right {
    height: 100%;
    padding: 10px;
    width: 88%;
    display: flex;
    justify-content: space-between;
    .amend {
      .amend_input_title {
        display: flex;
        align-items: center;
        padding-bottom: 15px;
      }
      .amend_input_list {
        width: 100%;
        display: flex;
        .input_list {
          height: 72vh;
          overflow: auto;
        }
      }
    }
    .datail_textarea {
      :deep(.ivu-input) {
        max-height: 200px;
      }
    }
    .addrel_icon {
      width: 100px;
      height: 100px;
    }
    .preselection_icon {
      margin-left: 10px;
      cursor: pointer;
      border-radius: 5px;
    }
    .preselection_icon:hover {
      border: 1px solid #7e7e7e;
    }
    .rel_icon_upload {
      margin-left: 5px;
      display: inline-block;
      width: 100px;
      height: 100px;
      .icon_upload {
        width: 100px;
        height: 100px;
        line-height: 100px;
      }
    }
  }
}
</style>
