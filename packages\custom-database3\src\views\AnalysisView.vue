<template>
  <!-- <iframe
    src="https://file.zhengzhoudacheng.com:8443"
    style="display: none;"
  ></iframe> -->
  <Filesystem
    :fileSocket="fileSocket"
    :title="'数据解析'"
    :fileSystemMenu="fileSystemMenu"
    :fileMenu="fileMenu"
  ></Filesystem>
  <!-- 文件解析 -->
  <Modal
    v-model="fileAnalysisModel"
    @on-visible-change="closeParseFile"
    :mask-closable="false"
    title="文件解析"
    width="75%"
    :styles="{ top: '50px' }"
  >
    <div class="file_parse">
      <Spin
        size="large"
        fix
        :show="
          searchStore.pathTreeLoad ||
          searchStore.caseTreeLoad ||
          analysisStore.OssDisposeLoad ||
          analysisStore.OssDataTypeLoad
        "
      ></Spin>
      <div class="parse_file">
        <Divider>任务信息</Divider>
        <Form
          ref="addFileAnalysisTaskRef"
          :model="addFileAnalysisTaskFrom"
          :rules="addFileAnalysisTaskRules"
          :label-width="100"
        >
          <FormItem label="任务名称：" prop="taskName">
            <Input v-model="addFileAnalysisTaskFrom.taskName" placeholder="请输入任务名称"></Input>
          </FormItem>
          <FormItem label="入库权限：" prop="parseDataBase">
            <Select v-model="addFileAnalysisTaskFrom.parseDataBase">
              <Option value="public">公共权限</Option>
              <Option value="authority">部门权限</Option>
              <Option value="username">用户权限</Option>
              <Option value="case">案件权限</Option>
            </Select>
          </FormItem>
          <FormItem
            label="选择案件："
            prop="caseArr"
            v-if="addFileAnalysisTaskFrom.parseDataBase === 'case'"
          >
            <Cascader
              v-model="addFileAnalysisTaskFrom.caseArr"
              filterable
              :data="searchStore.selectCaseData"
              placeholder="请选择案件"
              trigger="hover"
              v-width="600"
            />
          </FormItem>
          <FormItem label="入库路径：" prop="parsePath">
            <TreeSelect
              v-model="addFileAnalysisTaskFrom.parsePath"
              placeholder="请选择入库路径"
              :data="searchStore.allPathTreeData"
              v-width="600"
            />
          </FormItem>
          <FormItem label="图数据库：">
            <p>{{ addFileAnalysisTaskFrom.parseRelation }}</p>
            <Button type="info" @click="selectPathDatabase">选择图数据库</Button>
          </FormItem>
          <FormItem label="文件类型：" prop="fileType">
            <Select v-model="addFileAnalysisTaskFrom.fileType">
              <Option v-for="item in fileTypeList" :value="item.value" :key="item.value">{{
                item.label
              }}</Option>
            </Select>
          </FormItem>
          <FormItem label="分割字符：" prop="decollator">
            <Input
              v-model="addFileAnalysisTaskFrom.decollator"
              placeholder="请输入分割字符"
            ></Input>
          </FormItem>
        </Form>
        <div class="check_config">
          <Checkbox v-model="addFileAnalysisTaskFrom.simpleClean">数据简单清洗</Checkbox>
        </div>
      </div>
      <div class="oss_analysis">
        <Divider>解析模板</Divider>
        <!-- <div class="file_preview">
          预览文件：
          <span v-html="filePreviewText"></span>
        </div> -->
        <Select
          v-model="parseTelmplate"
          filterable
          @on-change="changeAnalysisField"
          label-in-value
          filter-by-label
          placeholder="请选择模板"
        >
          <Option
            v-for="(item, index) in analysisStore.ossAnalysisField"
            :value="item.value"
            :key="index"
            >{{ item.label }}</Option
          >
        </Select>
        <div class="oss_created">
          <div class="created_input_title">
            <p>模板名称：</p>
            <Input
              v-model="ossAnalysisFieldFrom.title"
              placeholder="请输入模板名称"
              style="width: 83%"
            />
          </div>
          <div class="created_input_list">
            <p>字段列表：</p>
            <div class="input_list">
              <Input
                style="width: 100%; margin-bottom: 15px"
                placeholder="请输入字段名称"
                v-for="(item, index) in ossAnalysisFieldFrom.fileNameArr"
                :key="index"
                v-model="item.field_name"
              >
                <template #prepend>
                  <Checkbox v-model="item.index">索引</Checkbox>
                  <Checkbox v-model="item.ignore">忽略</Checkbox>
                </template>
                <template #append>
                  <Button icon="ios-trash" @click="delFileInput(index)"></Button>
                </template>
              </Input>
            </div>
          </div>
          <div class="created_btn">
            <div>
              <Button icon="md-add" type="info" @click="addFileInput">添加字段</Button>
            </div>
            <div>
              <Checkbox v-model="isSaveNewTelmplate">保存为新模板</Checkbox>
            </div>
          </div>
        </div>
      </div>
      <div class="oss_data_type">
        <Divider>数据类型</Divider>
        <div class="data_type_list">
          <div
            v-for="item in analysisStore.OssDataTypeList"
            class="data_type_item"
            :class="item._id === nowSelectDataType?._id ? 'active' : ''"
            :key="item._id"
            @click="selectDataType(item)"
            :title="`名字:${item._source.name}\n描述:${item._source.detail}\n创建时间:${$tools.timestamp(item._source['@timestamp'])}`"
          >
            <img
              :src="
                '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' +
                item._source.icon
              "
            />
            <p>{{ item._source.nickname }}</p>
          </div>
        </div>
        <Form
          :model="addRelFrom"
          ref="addRelRef"
          :rules="addRelRules"
          label-position="left"
          :label-width="80"
        >
          <FormItem label="图标：" prop="icon">
            <div>
              <img
                class="addrel_icon"
                v-if="addRelFrom.icon"
                :src="
                  '/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' +
                  addRelFrom.icon
                "
              />
              <Upload
                :show-upload-list="false"
                :on-success="addRelIcon"
                :format="['jpg', 'jpeg', 'png']"
                :max-size="2048"
                :on-format-error="handleFormatError"
                :on-exceeded-size="handleMaxSize"
                :data="{ file_type: 'icon' }"
                name="put_small_file"
                type="drag"
                action="/filesystem/api/rest/v2/node-0/small_file/put_sha512_file"
                class="rel_icon_upload"
              >
                <div class="icon_upload">
                  <Icon type="ios-camera" size="20"></Icon>
                </div>
              </Upload>
              <template v-if="templateStore.iconList">
                <img
                  class="addrel_icon preselection_icon"
                  v-for="item in templateStore.iconList"
                  @click="setDataTypeIcon(item)"
                  :src="`/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/${item}`"
                />
              </template>
            </div>
          </FormItem>
          <FormItem label="名称：" prop="name">
            <Input v-model="addRelFrom.name"></Input>
          </FormItem>
          <FormItem label="昵称：" prop="nickname">
            <Input v-model="addRelFrom.nickname"></Input>
          </FormItem>
          <FormItem label="描述：" prop="detail">
            <Input
              type="textarea"
              :rows="3"
              v-model="addRelFrom.detail"
              class="datail_textarea"
            ></Input>
          </FormItem>
        </Form>
      </div>
    </div>
    <template #footer>
      <Button type="text" @click="cancelFileAnalysis">取 消</Button>
      <Button type="primary" @click="sendAnalysis">添 加</Button>
    </template>
  </Modal>
  <!-- csv文件解析 -->
  <Modal
    v-model="csvFileAnalysisModel"
    @on-visible-change="closeParseFile"
    :mask-closable="false"
    title="CSV文件解析"
    width="70%"
    :styles="{ top: '50px' }"
  >
    <div class="file_parse">
      <Spin size="large" fix :show="searchStore.pathTreeLoad"></Spin>
      <div class="parse_file">
        <Divider>任务信息</Divider>
        <Form
          ref="addCsvFileAnalysisTaskRef"
          :model="addCsvFileAnalysisTaskFrom"
          :rules="addCsvFileAnalysisRules"
          :label-width="100"
        >
          <FormItem label="任务名称：" prop="taskName">
            <Input
              v-model="addCsvFileAnalysisTaskFrom.taskName"
              placeholder="请输入任务名称"
            ></Input>
          </FormItem>
          <FormItem label="分割字符：" prop="decollator">
            <Input
              v-model="addCsvFileAnalysisTaskFrom.decollator"
              placeholder="请输入分割字符"
            ></Input>
          </FormItem>
          <FormItem label="入库路径：" prop="parsePath">
            <TreeSelect
              v-model="addCsvFileAnalysisTaskFrom.parsePath"
              placeholder="请选择入库路径"
              :data="searchStore.allPathTreeData"
              v-width="600"
            />
          </FormItem>
        </Form>
      </div>
      <!-- <div class="oss_analysis">
        <Divider>解析模板</Divider>
        <div class="file_preview">
          预览文件：
          <span v-html="csvFilePreviewText"></span>
        </div>
        <TreeSelect
          v-model="addCsvFileAnalysisTaskFrom.parsePath"
          placeholder="请选择入库路径"
          :data="searchStore.allPathTreeData"
          v-width="300"
        />
        <div class="oss_created">
          <div class="created_input_list">
            <p>字段列表：</p>
            <div class="input_list">
              <Input
                style="width: 100%; margin-bottom: 15px"
                placeholder="请输入字段名称"
                v-for="(item, index) in csvOssAnalysisFiledFrom.field_name"
                :key="index"
                v-model="csvOssAnalysisFiledFrom.field_name[index]"
              >
                <template #append>
                  <Button icon="ios-trash" @click="delCsvFileInput(index)"></Button>
                </template>
              </Input>
            </div>
          </div>
          <div class="created_btn">
            <div>
              <Button icon="md-add" type="info" @click="addCsvFileInput">添加字段</Button>
            </div>
            <div>
              <Checkbox v-model="isCsvSaveNewTelmplate">保存为新模板</Checkbox>
            </div>
          </div>
        </div>
      </div> -->
    </div>
    <template #footer>
      <Button type="text" @click="cancelCsvFileAnalysis">取 消</Button>
      <Button type="primary" @click="sendCsvAnalysis">添 加</Button>
    </template>
  </Modal>
  <!-- 选择图数据库 -->
  <GraphDatabase ref="graphDatabaseRef" @setDatabaseRel="setParentDatabaseRel" />
  <!-- 任务列表 -->
  <Modal
    v-model="viewTaskListModel"
    title="任务列表"
    footer-hide
    width="50%"
    @on-visible-change="closeTaskMdoel"
  >
    <div style="height: 600px; overflow: auto">
      <Table :columns="taskTableColumns" :data="analysisStore.parseTaskList">
        <template #name="{ row }">{{ row.columnValues.info.title }}</template>
        <template #state="{ row }">{{ parseTaskStatus(row.columnValues.info.status) }}</template>
        <template #plan="{ row }">
          <p v-if="row.columnValues.info.status === 'parse_ready'">未分析</p>
          <Progress
            v-else
            :percent="calculatProgress(row.columnValues.info)"
            :stroke-width="18"
            status="active"
            text-inside
          />
        </template>
        <template #operate="{ row }">
          <Button type="primary" size="small" style="margin-right: 5px" @click="reanalyse(row.row)"
            >重新分析</Button
          >
          <Button type="warning" size="small" style="margin-right: 5px" @click="checkLogs(row.row)"
            >日志</Button
          >
          <Button type="error" size="small" @click="delParesTask(row.row)">删除</Button>
        </template>
      </Table>
    </div>
  </Modal>
  <!-- 查看日志 -->
  <Modal v-model="checkTaskLogsModal" title="查看任务日志" width="50%" footer-hide>
    <div class="task_logs">
      <p class="item_logs" v-for="(item, index) in analysisStore.parseTaskLogs" :key="index">
        {{ item.columnValues.info.msg }}
      </p>
    </div>
  </Modal>
  <!-- 实时任务进度 -->
  <Modal
    v-model="RealTimeTaskModal"
    width="290"
    draggable
    sticky
    scrollable
    :mask="false"
    title="实时分析进度"
    footer-hide
    :styles="{ top: '0px', 'margin-right': '0px' }"
    @on-visible-change="closeRealTimeTaskModal"
  >
    <div class="realtime_task">
      <Divider plain>解析中</Divider>
      <template v-if="analysisStore.parsingTaskList.length">
        <div
          class="realtime_item"
          v-for="(item, index) in analysisStore.parsingTaskList"
          :key="index"
        >
          <p class="task_title" :title="item.columnValues.info.title">
            {{ item.columnValues.info.title }}
          </p>
          <Progress
            :percent="calculatProgress(item.columnValues.info)"
            status="active"
            :stroke-width="5"
          />
        </div>
      </template>
      <p v-else class="realtime_task_text">此状态下未查询到任务</p>
      <Divider plain>待解析</Divider>
      <template v-if="analysisStore.parseReadyTaskList.length">
        <div
          class="realtime_item"
          v-for="(item, index) in analysisStore.parseReadyTaskList"
          :key="index"
        >
          <p class="task_title">
            {{ item.columnValues.info.title }}
          </p>
          <Progress
            :percent="calculatProgress(item.columnValues.info)"
            status="active"
            :stroke-width="5"
          />
        </div>
      </template>
      <p v-else class="realtime_task_text">此状态下未查询到任务</p>
    </div>
  </Modal>
</template>

<script setup>
import axios from 'axios'
import { useAnalysisStore } from '@/stores/analysis'
import { useSearchStore } from '@/stores/search'
import { useTemplateStore } from '@/stores/template'
import { ref, inject } from 'vue'
import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css'
import { Modal, Message } from 'view-ui-plus'
import GraphDatabase from '@/components/GraphDatabase/GraphDatabaseView.vue'
const fileSocket = inject('$file')
const $tools = inject('$tools')
const searchStore = useSearchStore()
const templateStore = useTemplateStore()
const analysisStore = useAnalysisStore()
// 右键操作文件系统
const showTaskType = ref(null)
const fileSystemMenu = ref([
  // {
  //   label: '查看任务',
  //   icon: 'md-list',
  //   systemMenuCallBack: () => {
  //     viewTaskList()
  //   }
  // },
  {
    label: '查看CSV任务',
    icon: 'md-list',
    systemMenuCallBack: () => {
      viewCSVTaskList()
    }
  }
])
// 右键操作文件夹/文件
const checkFileType = ref('')
const nowFilePath = ref('')
const nowCsvFilePath = ref('')
const fileMenu = ref([
  // {
  //   label: '解析文件',
  //   icon: 'md-analytics',
  //   type: 'FILE',
  //   fileMenuCallBack: (type, path, item) => {
  //     checkFileType.value = type
  //     nowFilePath.value = path
  //     fileAnalysis(item)
  //   }
  // },
  {
    label: '解析CSV',
    icon: 'md-analytics',
    // type: 'FILE',
    fileMenuCallBack: (type, path, item) => {
      checkFileType.value = type
      nowCsvFilePath.value = path
      csvFileAnalysis(item)
    }
  }
])
// 查看任务列表
const viewTaskListModel = ref(false)
const taskTimer = ref(null)
const viewTaskList = () => {
  analysisStore.setTaskType('default')
  if (viewTaskListModel.value) viewTaskListModel.value = false
  if (taskTimer.value) {
    clearInterval(taskTimer.value)
    taskTimer.value = null
  }
  analysisStore.getParseTaskList()
  viewTaskListModel.value = true
  taskTimer.value = setInterval(() => {
    analysisStore.getParseTaskList()
  }, 1500)
}
//查看csv任务列表
const viewCSVTaskList = () => {
  analysisStore.setTaskType('csv')
  if (viewTaskListModel.value) viewTaskListModel.value = false
  if (taskTimer.value) {
    clearInterval(taskTimer.value)
    taskTimer.value = null
  }
  analysisStore.getParseTaskList()
  viewTaskListModel.value = true
  taskTimer.value = setInterval(() => {
    analysisStore.getParseTaskList()
  }, 1500)
}
const closeTaskMdoel = (bool) => {
  if (!bool) {
    clearInterval(taskTimer.value)
  }
}
const delParesTask = (row) => {
  Modal.confirm({
    title: '提示',
    content: '此操作将永久删除该任务,是否继续?',
    onOk: () => {
      analysisStore.sendDelParseTask(row)
    },
    onCancel: () => {
      Message.info('取消删除')
    }
  })
}
const reanalyse = (row) => {
  analysisStore.sendTaskReanalyse(row)
}
const checkTaskLogsModal = ref(false)
const checkLogs = (row) => {
  analysisStore.sendParseTaskLogs(row)
  checkTaskLogsModal.value = true
}
// 计算进度
const calculatProgress = (item) => {
  if (!item?.all_num_received) {
    return 0
  } else {
    const percentage = (item.all_num_received / item.all_num) * 100
    return Number(percentage.toFixed(1))
  }
}
const taskTableColumns = ref([
  {
    title: '任务名称',
    slot: 'name',
    width: 160,
    align: 'center'
  },
  {
    title: '任务状态',
    slot: 'state',
    width: 160,
    align: 'center',
    filters: [
      {
        label: '准备解析',
        value: 'parse_ready'
      },
      {
        label: '解析中',
        value: 'parsing'
      },
      {
        label: '解析完成',
        value: 'parse_end'
      },
      {
        label: '出错',
        value: 'error'
      }
    ],
    filterMultiple: false,
    filterMethod(value, row) {
      switch (value) {
        case 'parse_ready':
          return row.columnValues.info.status === value
        case 'parsing':
          return row.columnValues.info.status === value
        case 'parse_end':
          return row.columnValues.info.status === value
        case 'error':
          return row.columnValues.info.status === value
      }
    }
  },
  {
    title: '解析进度',
    slot: 'plan',
    width: 380,
    align: 'center'
  },
  {
    title: '操作',
    slot: 'operate',
    width: 225,
    align: 'center'
  }
])
const parseTaskStatus = (status) => {
  switch (status) {
    case 'parse_ready':
      return '准备解析'
    case 'parsing':
      return '解析中'
    case 'parse_end':
      return '解析完成'
    case 'error':
      return '出错'
    default:
      return '未知'
  }
}
// 实时任务进度
const RealTimeTaskModal = ref(false)
const realTimer = ref(null)
const checkRealTimeTaskModal = () => {
  RealTimeTaskModal.value = true
  realTimer.value = setInterval(() => {
    if (analysisStore.parsingTaskList.length <= 0 && analysisStore.parseReadyTaskList.length <= 0) {
      RealTimeTaskModal.value = false
    } else {
      analysisStore.getParsingTaskList()
    }
  }, 1500)
}
const closeRealTimeTaskModal = (bool) => {
  if (!bool) {
    clearInterval(realTimer.value)
  }
}
// 文件预览
const filePreviewText = ref('')
const filePreview = (item) => {
  const previewFileUrl = `/filesystem/api/rest/v2/node-0/main_file/get/${checkFileType.value}/${nowFilePath.value}/${item.pathSuffix}`
  fetch(previewFileUrl, {
    method: 'GET',
    headers: {
      Range: 'bytes=0-100'
    }
  })
    .then((res) => {
      if (res.ok) {
        return res.text()
      } else {
        filePreviewText.value = "<span style='color:red'>预览文件失败...</span>"
      }
    })
    .then((res) => {
      filePreviewText.value = `<span>${res}</span>`
    })
}

// csv文件解析
const nowCsvAddTaskFile = ref(null)
const csvFileAnalysisModel = ref(false)
const addCsvFileAnalysisTaskRef = ref(null)
const addCsvFileAnalysisTaskFrom = ref({
  taskName: '',
  parseDataBase: 'public',
  decollator: '',
  parsePath: ''
})
const addCsvFileAnalysisRules = ref({
  taskName: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
  decollator: [{ required: true, message: '分割字符不能为空', trigger: 'blur' }],
  parsePath: [{ required: true, message: '请选择数据入库路径', trigger: 'change' }]
})
const csvParseTelmplate = ref('')
const csvOssAnalysisFiledFrom = ref({
  path: '',
  field_name: []
})
const isCsvSaveNewTelmplate = ref(false)
const addCsvFileInput = (item) => {
  if (item) {
    csvOssAnalysisFiledFrom.value.field_name.push(item)
  } else {
    csvOssAnalysisFiledFrom.value.field_name.push()
  }
}
const delCsvFileInput = (index) => {
  csvOssAnalysisFiledFrom.value.field_name.splice(index, 1)
}
const csvFileAnalysis = (item) => {
  analysisStore.setTaskType('csv')
  csvOssAnalysisFiledFrom.value.path = ''
  csvOssAnalysisFiledFrom.value.field_name = []
  searchStore.pathTreeLoad = true
  const selectPathData = localStorage.getItem('selectPathData')
  searchStore.clearPathTreeData()
  if (selectPathData) {
    searchStore.allPathTreeData = JSON.parse(selectPathData)
    searchStore.pathTreeLoad = false
  } else {
    searchStore.getPathTreeData()
  }
  // csvFilePreview()
  csvFileAnalysisModel.value = true
  nowCsvAddTaskFile.value = item
}
// 读取模板文件
const csvFilePreviewText = ref('')
const csvFilePreview = () => {
  const previewFileUrl = `/filesystem/api/rest/v2/node-0/main_file/get/public/custom_database_2.json`
  fetch(previewFileUrl, {
    method: 'GET',
    headers: {
      Range: 'bytes=0-100'
    }
  })
    .then((res) => {
      console.log('previewFileUrl:', res)
      if (res.ok) {
        return res.text()
      } else {
        csvFilePreviewText.value = "<span style='color:red'>预览模板失败...</span>"
      }
    })
    .then((res) => {
      csvFilePreviewText.value = `<span>${res}</span>`
      // 获取第二层 key 的函数
      const getSecondLevelKeys = (res) => {
        let secondLevelKeys = []
        for (let firstLevelKey in res) {
          csvOssAnalysisFiledFrom.value.path = firstLevelKey
          if (res.hasOwnProperty(firstLevelKey) && typeof res[firstLevelKey] === 'object') {
            secondLevelKeys = secondLevelKeys.concat(Object.keys(res[firstLevelKey]))
          }
        }
        return secondLevelKeys
      }
      // 调用函数并输出结果
      const keys = getSecondLevelKeys(JSON.parse(res))
      for (let index = 0; index < keys.length; index++) {
        const element = keys[index]
        addCsvFileInput(element)
      }
      console.log('csvOssAnalysisFiledFrom:', csvOssAnalysisFiledFrom.value.field_name)
    })
}
//转换函数
const transformCustom = (custom) => {
  const result = {}
  const { path, field_name } = custom
  result[path] = field_name.reduce((acc, field) => {
    acc[field] = true
    return acc
  }, {})
  return result
}
// 添加csv任务
const sendCsvAnalysis = () => {
  console.log('sendCsvAnalysis')
  //保存模板
  if (isCsvSaveNewTelmplate.value) {
    let url = window.location.origin + '/filesystem/api/rest/v2/node-0/main_file/put'
    console.log('csvOssAnalysisFiledFrom:', csvOssAnalysisFiledFrom.value)
    let custom_database_2_json = transformCustom(csvOssAnalysisFiledFrom.value)
    console.log('custom_database_2_json:', custom_database_2_json)
    const json = JSON.stringify(custom_database_2_json)
    const blob = new Blob([json], {
      type: 'application/json'
    })
    const fromData = new FormData()
    fromData.append('put_main_file', blob)
    fromData.append('file_name', 'custom_database_2.json')
    fromData.append('file_type', 'public')
    fromData.append('file_path', '')
    axios
      .post(url, fromData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then((res) => {
        console.log('上传成功!')
      })
      .catch((err) => {
        console.log('上传失败!', err)
      })
  }
  analysisStore.setTaskType('csv')
  //创建任务
  analysisStore.sendAddParseCsvFileTask({
    fields: csvOssAnalysisFiledFrom.value.field_name,
    databasePath: nowCsvFilePath.value + '/' + nowCsvAddTaskFile.value.pathSuffix,
    databaseType: 'csv',
    ...addCsvFileAnalysisTaskFrom.value
  })
  if (realTimer.value) {
    clearInterval(realTimer.value)
    RealTimeTaskModal.value = false
  }
  cancelCsvFileAnalysis()
  checkCSVRealTimeTaskModal()
}
//取消csv任务
const cancelCsvFileAnalysis = () => {
  addCsvFileAnalysisTaskRef.value.resetFields()
  csvOssAnalysisFiledFrom.value = {
    path: '',
    field_name: []
  }
  csvFilePreviewText.value = ''
  isCsvSaveNewTelmplate.value = false
  csvFileAnalysisModel.value = false
}
//csv任务进度
const checkCSVRealTimeTaskModal = () => {
  RealTimeTaskModal.value = true
  realTimer.value = setInterval(() => {
    if (analysisStore.parsingTaskList.length <= 0 && analysisStore.parseReadyTaskList.length <= 0) {
      RealTimeTaskModal.value = false
    } else {
      analysisStore.getParseTaskList()
    }
  }, 1500)
}

// 文件解析
const fileAnalysisModel = ref(false)
const addFileAnalysisTaskRef = ref(null)
const fileTypeList = ref([
  {
    value: 'csv',
    label: 'Csv文件格式'
  },
  {
    value: 'mysql_sql',
    label: 'MySql文件格式'
  }
])
const addFileAnalysisTaskFrom = ref({
  taskName: '', //任务名称
  parseDataBase: 'public', //数据入库权限
  caseArr: [],
  caseId: '', //数据入库案件id
  parsePath: '', //数据入库路径
  parseRelation: '', //数据入库relation字符
  fileType: '', //解析文件类型
  decollator: ',', //解析文件分割字符
  simpleClean: false //文件是否简单清洗
})
const addFileAnalysisTaskRules = ref({
  taskName: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
  parseDataBase: [{ required: true, message: '请选择数据入库权限', trigger: 'blur' }],
  caseArr: [{ required: true, type: 'array', min: 1, message: '请选择案件', trigger: 'change' }],
  parsePath: [{ required: true, message: '请选择数据入库路径', trigger: 'change' }],
  fileType: [{ required: true, message: '请选择文件解析格式类型', trigger: 'blur' }],
  decollator: [{ required: true, message: '分割字符不能为空', trigger: 'blur' }]
})
// 操作模板
const parseTelmplate = ref('')
const ossAnalysisFieldFrom = ref({
  title: '',
  fileNameArr: [
    {
      field_name: '',
      index: true,
      ignore: false
    }
  ]
})
const isSaveNewTelmplate = ref(false)
const addFileInput = () => {
  ossAnalysisFieldFrom.value.fileNameArr.push({
    field_name: '',
    index: true,
    ignore: false
  })
}
// 删除字段
const delFileInput = (index) => {
  ossAnalysisFieldFrom.value.fileNameArr.splice(index, 1)
}
const changeAnalysisField = (item) => {
  if (item) {
    const { label, value } = item
    ossAnalysisFieldFrom.value.title = label
    ossAnalysisFieldFrom.value.fileNameArr = JSON.parse(value)
  }
}
// 操作类型
const addRelFrom = ref({
  icon: '',
  name: '',
  nickname: '',
  detail: ''
})
const addRelRules = ref({
  icon: [{ required: true, message: '数据类型图标不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '数据类型名称不能为空', trigger: 'blur' }],
  nickname: [{ required: true, message: '数据类型昵称不能为空', trigger: 'blur' }],
  detail: [{ required: true, message: '数据类型描述不能为空', trigger: 'blur' }]
})
const addRelRef = ref(null)
const handleFormatError = () => {
  Message.warning({
    background: true,
    content: '不支持的图片格式，仅支持jpg和png格式'
  })
}
const handleMaxSize = () => {
  Message.warning({
    background: true,
    content: '图片大小不能超过2M'
  })
}
const addRelIcon = (res) => {
  addRelFrom.value.icon = res
}
// 设置图标
const setDataTypeIcon = (item) => {
  addRelFrom.value.icon = item
}
const nowSelectDataType = ref(null)
const selectDataType = (item) => {
  nowSelectDataType.value = item
  addRelFrom.value.icon = item._source.icon
  addRelFrom.value.name = item._source.name
  addRelFrom.value.nickname = item._source.nickname
  addRelFrom.value.detail = item._source.detail
}
// 判断是否需要新建数据类型
const isCreatedNewDataType = () => {
  if (nowSelectDataType.value) {
    return !(
      nowSelectDataType.value._source.icon === addRelFrom.value.icon &&
      nowSelectDataType.value._source.name === addRelFrom.value.name &&
      nowSelectDataType.value._source.nickname === addRelFrom.value.nickname &&
      nowSelectDataType.value._source.detail === addRelFrom.value.detail
    )
  } else {
    return true
  }
}
const nowAddTaskFile = ref(null)
const fileAnalysis = (item) => {
  analysisStore.setTaskType('default')
  searchStore.caseTreeLoad = true
  searchStore.pathTreeLoad = true
  analysisStore.OssDisposeLoad = true
  analysisStore.OssDataTypeLoad = true
  analysisStore.getDataTypeOss()
  // 获取路径树数据
  const selectPathData = localStorage.getItem('selectPathData')
  searchStore.clearPathTreeData()
  if (selectPathData) {
    searchStore.allPathTreeData = JSON.parse(selectPathData)
    searchStore.pathTreeLoad = false
  } else {
    searchStore.getPathTreeData()
  }
  // 获取案件数据
  const selectCaseData = localStorage.getItem('selectCaseData')
  searchStore.clearCaseTreeData()
  if (selectCaseData) {
    searchStore.selectCaseData = JSON.parse(selectCaseData)
    searchStore.caseTreeLoad = false
  } else {
    searchStore.getCaseTreeData()
  }
  analysisStore.getAnalysisOssDispose()
  filePreviewText.value = ''
  // filePreview(item)
  fileAnalysisModel.value = true
  nowAddTaskFile.value = item
}

// 选择relation
const setParentDatabaseRel = (relStr) => {
  addFileAnalysisTaskFrom.value.parseRelation = relStr
}
// 选择图数据库
const graphDatabaseRef = ref(null)
const selectPathDatabase = () => {
  if (addFileAnalysisTaskFrom.value.parsePath) {
    if (addFileAnalysisTaskFrom.value.parseDataBase === 'case') {
      if (addFileAnalysisTaskFrom.value.caseArr.length) {
        addFileAnalysisTaskFrom.value.caseId =
          addFileAnalysisTaskFrom.value.caseArr[addFileAnalysisTaskFrom.value.caseArr.length - 1]
      } else {
        Message.warning({
          background: true,
          content: '请先选择案件'
        })
        return
      }
    }
    graphDatabaseRef.value.showGraphDatabase()
    analysisStore.initSearchDatabase({
      value: '',
      database: addFileAnalysisTaskFrom.value.parseDataBase,
      caseId: addFileAnalysisTaskFrom.value.caseId,
      path: addFileAnalysisTaskFrom.value.parsePath
    })
  } else {
    Message.warning({
      background: true,
      content: '请先选择入库路径'
    })
  }
}
// 提交解析任务
const sendAnalysis = () => {
  let formA = false
  let formB = false
  addFileAnalysisTaskRef.value.validate((valid) => {
    formA = valid
  })
  addRelRef.value.validate((valid) => {
    formB = valid
  })
  if (addFileAnalysisTaskFrom.value.parseDataBase === 'case') {
    if (addFileAnalysisTaskFrom.value.caseArr.length) {
      addFileAnalysisTaskFrom.value.caseId =
        addFileAnalysisTaskFrom.value.caseArr[addFileAnalysisTaskFrom.value.caseArr.length - 1]
    } else {
      Message.warning({
        background: true,
        content: '请先选择案件'
      })
      return
    }
  }
  if (formA && formB) {
    // 保存模板
    if (isSaveNewTelmplate.value) {
      analysisStore.sendAddAnalysisOss({
        id: String(Math.floor(Math.random() * 10000000000)),
        ...ossAnalysisFieldFrom.value
      })
    }
    // 保存类型
    if (isCreatedNewDataType()) {
      analysisStore.sendAddDataTyprOss({
        id: String(Math.floor(Math.random() * 10000000000)),
        ...addRelFrom.value
      })
    }
    // 创建任务
    analysisStore.sendAddParseFileTask({
      data_type: addRelFrom.value,
      fields: ossAnalysisFieldFrom.value.fileNameArr,
      databasePath: nowFilePath.value + '/' + nowAddTaskFile.value.pathSuffix,
      databaseType: checkFileType.value,
      ...addFileAnalysisTaskFrom.value
    })
    cancelFileAnalysis()
    checkRealTimeTaskModal()
  }
}
const closeParseFile = (bool) => {
  if (!bool) {
    cancelFileAnalysis()
  }
}
const cancelFileAnalysis = () => {
  addFileAnalysisTaskRef.value.resetFields()
  addRelRef.value.resetFields()
  ossAnalysisFieldFrom.value = {
    title: '',
    fileNameArr: [
      {
        field_name: '',
        index: true,
        ignore: false
      }
    ]
  }
  parseTelmplate.value = ''
  filePreviewText.value = ''
  nowSelectDataType.value = null
  isSaveNewTelmplate.value = false
  fileAnalysisModel.value = false
}
</script>

<style lang="scss" scoped>
.analysis_header {
  width: 100%;
  margin-top: 10px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .header_text {
    font-size: 24px;
    padding: 10px 0 0 10px;
  }
  .his_path_body {
    display: flex;
    align-items: center;
    margin-left: 10px;
    margin-bottom: 10px;
    margin-top: 8px;
    .field_path {
      width: 93%;
      display: flex;
      align-items: center;
      .path_list {
        margin-left: 5px;
        display: flex;
        align-items: center;
        .path_item {
          cursor: pointer;
        }
        .active_path {
          border: 1px solid #118d11 !important;
        }
        .path_item:hover {
          border: 1px solid #2d8cf0 !important;
        }
      }
    }
  }
}
.file_list {
  margin: 5px;
  width: 100%;
  height: 80vh;
  background-color: #fff;
  position: relative;
  overflow: auto;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  .file_item {
    width: 7.1%;
    white-space: normal;
    min-height: 100px;
    border-radius: 5px;
    margin: 5px;
    text-align: center;
    cursor: pointer;
    padding: 10px 5px 5px;
    .file_name {
      width: 100%;
      color: #000;
      word-break: break-all;
    }
    img {
      width: 40px;
    }
    .file_input {
      width: 100%;
      padding: 5px;
      color: #000;
      resize: none;
      min-height: 28px;
    }
    .file_input:focus {
      outline: none;
    }
  }
  .file_item:hover {
    background-color: rgb(156, 181, 228);
  }
}
.upload_text {
  height: 200px;
  display: grid;
  align-content: center;
}

.file_parse {
  position: relative;
  width: 100%;
  height: 700px;
  display: flex;
  .parse_file {
    width: 33%;
    padding-right: 10px;
    .check_config {
      display: flex;
      justify-content: flex-end;
    }
  }
  .oss_analysis {
    width: 67%;
    border-left: 1px solid #e8eaec;
    border-right: 1px solid #e8eaec;
    padding: 0 10px;
    .oss_created {
      margin-top: 10px;
      .created_input_title {
        width: 100%;
        display: flex;
        align-items: center;
        padding-bottom: 15px;
      }
      .created_input_list {
        width: 100%;
        display: flex;
        .input_list {
          height: 480px;
          width: 83%;
          overflow: auto;
        }
      }

      .created_btn {
        margin-top: 10px;
        width: 86%;
        display: flex;
        justify-content: space-between;
        float: right;
      }
    }
    .file_preview {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      overflow: hidden;
      width: 100%;
    }
  }
  .oss_data_type {
    width: 33%;
    padding-left: 10px;
    .data_type_list {
      height: 245px;
      border-bottom: 1px solid #e8eaec;
      margin-bottom: 15px;
      overflow: auto;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      .data_type_item {
        width: 80px;
        height: 80px;
        text-align: center;
        cursor: pointer;

        border-radius: 5px;
        img {
          width: 50px;
        }
        p {
          width: 80px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .data_type_item:hover {
        background-color: #b7d9fa;
      }
      .active {
        background-color: #b7d9fa;
      }
    }
    .datail_textarea {
      :deep(.ivu-input) {
        max-height: 200px;
      }
    }
    .addrel_icon {
      width: 60px;
      height: 60px;
    }
    .preselection_icon {
      margin-left: 10px;
      cursor: pointer;
      border-radius: 5px;
    }
    .preselection_icon:hover {
      border: 1px solid #7e7e7e;
    }
    .rel_icon_upload {
      margin-left: 5px;
      display: inline-block;
      width: 60px;
      height: 60px;
      .icon_upload {
        width: 60px;
        height: 60px;
        line-height: 60px;
      }
    }
  }
}
.task_logs {
  height: 600px;
  overflow: auto;
  .item_logs {
    padding: 5px 0;
    border-bottom: 1px solid #c8cbce;
  }
}
.realtime_task {
  width: 100%;
  max-height: 870px;
  overflow: auto;
  .realtime_item {
    margin-bottom: 10px;
    .task_title {
      color: #000;
    }
    :deep(.ivu-progress-show-info .ivu-progress-outer) {
      padding-right: 28px;
      margin-right: -30px;
    }
  }
}
.realtime_task_text {
  text-align: center;
  line-height: 50px;
}
:deep(.ivu-form-item-content) {
  display: flex;
  justify-content: space-between;
}
:deep(.ivu-divider-horizontal.ivu-divider-with-text-center) {
  margin: 0;
}
</style>
