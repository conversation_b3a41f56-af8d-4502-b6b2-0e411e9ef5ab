<template>
  <div class="tree_layout">
    <Tree
      :data="data"
      :load-data="loadData"
      :render="renderContent"
      class="demo-tree-render"
      @on-toggle-expand="handleExpand"
      @on-select-change="handleSelectChange"
      :key="treeKey"
    >
    </Tree>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, computed, resolveComponent, nextTick, onMounted } from 'vue'
import { useLoginStore } from '@/stores/login'

import { DocumentAdd, Delete, Edit, FolderAdd } from '@element-plus/icons-vue'
import tools from '@/utils/tools'
const loginStore = useLoginStore()

const emit = defineEmits(['getCollect'])
//数据
let data = ref([
  {
    id: 0,
    title: '/',
    loading: false,
    children: [],
    type: 'dirFather',
    loaded: false,
    showInput: false
  }
])
let treeKey = ref(0)
let buttonProps = ref({
  type: 'primary',
  size: 'mini'
})
const props = defineProps({
  listType: {
    type: String,
    default: () => ({})
  },
  tableName: {
    type: String,
    default: () => ({})
  },
  getCollect: {
    type: Function,
    default: () => ({})
  }
})
//方法
//挂载完毕onMounted
onMounted(() => {
  console.log('挂在完毕')
  loadData(data.value[0], (children) => {
    data.value[0].children = children
    data.value[0].loading = false
    data.value[0].expand = true
  })
})
const handleExpand = (node) => {
  console.log('handleExpand', node)
  if (node.expand && node.children && node.children.length > 0) {
    // 节点已展开且有子节点时，强制重新加载
    loadData(node, (children) => {
      node.children = children
      node.loading = false
    })
  }
}

const handleSelectChange = (node, data) => {
  if (data.type === 'file') {
    console.log('点击了文件:', data)
    emit('getCollect', data)
  } else if (data.type === 'dir' || data.type === 'dirFather') {
    if (data.expand) {
      data.expand = false
    } else {
      if (!data.loaded) {
        data.loading = true
        loadData(data, (children) => {
          data.children = children
          data.loading = false
          data.expand = true
        })
      } else {
        data.expand = true
      }
    }
  }
}

// 修改名字
const amendName = (root, node, data) => {
  data.showInput = true
}

const saveName = (root, node, data) => {
  console.log('saveName', root, node, data)
  const parentKey = root.find((el) => el === node).parent
  const parent = root.find((el) => el.nodeKey === parentKey).node
  const index = parent.children.indexOf(data)
  data.showInput = false
  const { $data } = useLoginStore().wsMethod
  // 注意：这里的 'Api.Search.SearchPrefixTable.UpdateData' 是一个示例路径，
  // 你需要根据你的后端API进行调整。
  $data.sendData(
    'Api.Search.SearchPrefixTable.AddData',
    [
      {
        msg: {
          type: props.listType,
          authority: loginStore.userinfo.authority,
          username: loginStore.userinfo.username,
          table: props.tableName,
          prefix: data.id,
          relation: parent.id ? `${parent.id};${data.type}` : '',

          data: {
            data: {
              name: {
                name: data.title
              },
              _: {
                _: data.id
              }
            }
          }
        }
      }
    ],
    (res) => {
      if (res?.status === 'ok') {
        ElMessage({
          message: '修改成功!',
          type: 'success'
        })
      } else {
        ElMessage({
          message: '修改失败!',
          type: 'warning'
        })
      }
    }
  )
}

//异步加载子节点
const loadData = (data, callback) => {
  console.log('loadData', data)
  const { $data } = useLoginStore().wsMethod

  let mydata = []

  let dirfn = (callback, msg) => {
    $data.sendData(
      'Api.Search.SearchPrefixTable.Query',
      [
        {
          head: {
            size: 200
          },
          msg: msg
        }
      ],
      (res) => {
        console.log('res', res)
        if (!res || res.length === 0) {
          data.isEmpty = true
        }
        res?.forEach((item) => {
          let obj = new Object()
          if (item.columnValues.d.type.type === 'file') {
            obj = {
              id: item.columnValues.d._._,
              title: item.columnValues.d.name.name,
              row: item.row,
              type: item.columnValues.d.type.type === 'dir' ? 'dir' : 'file',
              showInput: false
            }
          }
          if (item.columnValues.d.type.type === 'dir') {
            obj = {
              id: item.columnValues.d._._,
              title: item.columnValues.d.name.name,
              loading: false,
              row: item.row,
              type: item.columnValues.d.type.type === 'dir' ? 'dir' : 'file',
              showInput: false,
              children: []
            }
          }
          mydata.push(obj)
        })
        callback(mydata)
        data.loaded = true
        treeKey.value++
        console.log('mydata', mydata, callback, treeKey.value)
      }
    )
  }

  let dirFileFn = (callback, msgA, msgB) => {
    $data.sendData(
      'Api.Search.SearchPrefixTable.Query',
      [
        {
          head: {
            size: 200
          },
          msg: msgA
        }
      ],
      (res1) => {
        res1?.forEach((item) => {
          let obj = new Object()

          if (item.columnValues.d.type.type === 'dir') {
            obj = {
              id: item.columnValues.d._._,
              title: item.columnValues.d.name.name,
              loading: false,
              row: item.row,
              type: item.columnValues.d.type.type === 'dir' ? 'dir' : 'file',
              showInput: false,
              children: []
            }
          }
          mydata.push(obj)
        })

        $data.sendData(
          'Api.Search.SearchPrefixTable.Query',
          [
            {
              head: {
                size: 200
              },
              msg: msgB
            }
          ],
          (res2) => {
            if (res1.length == 0 && res2.length == 0) {
              ElMessage({
                type: 'info',
                message: '该目录为空'
              })
              //mydata.push({ title: '该目录为空', type: 'nodata' })
              data.isEmpty = true
            }
            res2?.forEach((item) => {
              let obj = new Object()
              if (item.columnValues.d.type.type === 'file') {
                obj = {
                  id: item.columnValues.d._._,
                  title: item.columnValues.d.name.name,
                  row: item.row,
                  type: item.columnValues.d.type.type === 'dir' ? 'dir' : 'file',
                  showInput: false
                }
              }

              mydata.push(obj)
            })

            callback(mydata)
            data.loaded = true
            treeKey.value++
          }
        )
      }
    )
  }
  if (data.id != 0) {
    let msgA = {}
    let msgB = {}
    msgA = {
      table: props.tableName,
      prefix: '',
      type: props.listType,
      relation: data.id + ';dir'
    }
    msgB = {
      table: props.tableName,
      prefix: '',
      type: props.listType,
      relation: data.id + ';file'
    }

    // dirfn(callback, msgA)
    dirFileFn(callback, msgA, msgB)
  } else {
    let msg = {}
    msg = {
      table: props.tableName,
      prefix: '',
      type: props.listType
    }
    dirfn(callback, msg)
  }
}
const addFilePath = (v) => {
  console.log('addFilePath', v)
  $data.sendData(
    'Api.Search.SearchPrefixTable.AddData',
    [
      {
        msg: {
          type: props.listType,
          authority: loginStore.userinfo.authority,
          username: loginStore.userinfo.username,
          table: props.tableName,
          prefix: v.random,

          relation: v.data.id ? `${v.data.id};${v.type}` : '',
          data: {
            data: {
              otherSystem: 'intelligent',
              name: {
                name: v.name
              },
              type: {
                type: v.type
              },
              _: {
                _: v.random
              }
            }
          }
        }
      }
    ],
    (res) => {
      if (res?.status === 'ok') {
        // 节点已展开且有子节点时，强制重新加载
        loadData(v.data, (children) => {
          v.data.children = children
          v.data.loading = false
        })
        ElMessage({
          message: '添加成功!',
          type: 'success'
        })
      } else {
        ElMessage({
          message: '添加失败!',
          type: 'warning'
        })
      }
    }
  )
}
// 生成随机数+当前时间戳
const createRandom = () => {
  let randomNum = Math.floor(Math.random() * 100000).toString()
  return 2 ** 31 - new Date().getTime() / 1000 + randomNum
}
const folderAppend = (data) => {
  data.isEmpty = false // 添加节点，父节点不再为空
  const children = data.children || []
  let newId = createRandom()
  addFilePath({
    data: data,
    random: newId,
    type: 'dir',
    name: '新目录'
  })

  let Id = data.id
  let row = ''
  if (Id === 0) {
    row =
      tools.sha512('p;' + props.tableName).toString('hex') + ';p;' + props.tableName + ';' + newId
  } else {
    row =
      tools.sha512('p;' + props.tableName + ';' + Id + ';dir').toString('hex') +
      ';p;' +
      props.tableName +
      ';' +
      Id +
      ';dir;' +
      newId
  }
  children.push({
    title: '新目录',
    row: row,
    expand: false,
    loading: false,
    children: [],
    type: 'dir'
  })
  data.children = children
}
const documentAppend = (data) => {
  data.isEmpty = false // 添加节点，父节点不再为空
  const children = data.children || []
  let newId = createRandom()
  addFilePath({
    data: data,
    random: newId,
    type: 'file',
    name: '新文件'
  })

  let Id = data.id
  let row = ''
  if (Id === 0) {
    row =
      tools.sha512('p;' + props.tableName).toString('hex') + ';p;' + props.tableName + ';' + newId
  } else {
    row =
      tools.sha512('p;' + props.tableName + ';' + Id + ';dir').toString('hex') +
      ';p;' +
      props.tableName +
      ';' +
      Id +
      ';dir;' +
      newId
  }
  children.push({
    title: '新文件',
    row: row,
    type: 'file'
  })
  data.children = children
}
const sendDelNode = (data) => {
  $data.sendData(
    'Api.Search.SearchPrefixTable.DelData',
    [
      {
        head: {
          row_key: [data.row]
        },
        msg: {
          type: props.listType,
          authority: loginStore.userinfo.authority,
          username: loginStore.userinfo.username,
          table: props.tableName,
          relation: data.fatherId ? `${data.fatherId};${data.type}` : ''
        }
      }
    ],
    (res) => {
      if (res?.status === 'ok') {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
      } else {
        ElMessage({
          type: 'warning',
          message: '删除失败'
        })
      }
    }
  )
}
const remove = (root, node, data) => {
  const parentKey = root.find((el) => el === node).parent
  const parent = root.find((el) => el.nodeKey === parentKey).node
  const index = parent.children.indexOf(data)
  /* console.log('root, node, data', root, node, data, parent) */
  ElMessageBox.confirm('确定删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      sendDelNode({
        fatherId: parent.id,
        row: data.row,
        type: data.type
      })

      parent.children.splice(index, 1)
      if (parent.children.length === 0) {
        parent.isEmpty = true // 如果删除后没有子节点，则标记为empty
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消删除'
      })
    })
}
const renderContent = (h, { root, node, data }) => {
  if (data.type === 'nodata') {
    return h(
      'span',
      {
        style: {
          color: '#cccc',
          paddingLeft: '28px', // 模拟没有图标的缩进
          display: 'inline-block',
          width: 'calc(100% - 80px)',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          verticalAlign: 'middle'
        },
        domProps: {
          title: data.title
        }
      },
      data.title
    )
  }
  // 根据节点类型设置样式类名
  const nodeClass = [data.type === 'dir' ? 'tree-dir' : 'tree-leaf']
  if (data.isEmpty) {
    nodeClass.push('is-empty')
  }
  return h(
    'span',
    {
      style: {
        width: '100%'
      },

      class: nodeClass // 添加动态类名
    },
    [
      h('span', [
        h(resolveComponent('Icon'), {
          type:
            data.type === 'dir' || data.type === 'dirFather'
              ? 'ios-folder-outline'
              : 'ios-paper-outline',
          style: {
            marginRight: '8px'
          }
        }),

        data.showInput
          ? h(resolveComponent('Input'), {
              modelValue: data.title,
              'onUpdate:modelValue': (val) => {
                data.title = val
              },
              onOnBlur: () => {
                saveName(root, node, data)
              },
              // 自动聚焦
              ref: (input) => {
                if (input) {
                  nextTick(() => {
                    input.focus()
                  })
                }
              }
            })
          : h(
              'span',
              {
                style: {
                  display: 'inline-block',
                  width: 'calc(100% - 125px)',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  verticalAlign: 'middle'
                },
                title: data.title
              },
              data.title
            )
      ]),
      h(
        'span',
        {
          style: {
            float: 'right',
            marginLeft: '15px',
            display: 'block',
            marginTop: '5px'
          }
        },
        [
          h(resolveComponent('DocumentAdd'), {
            ...buttonProps,
            icon: 'DocumentAdd',
            title: '添加文件',
            style: {
              width: '1em',
              height: '1em',
              display: data.type === 'dir' || data.type === 'dirFather' ? 'inline-block' : 'none',
              marginRight: '8px'
            },
            onClick: (event) => {
              event.stopPropagation()
              documentAppend(data)
            }
          }),
          h(resolveComponent('FolderAdd'), {
            ...buttonProps,
            icon: 'FolderAdd',
            title: '添加目录',
            style: {
              width: '1em',
              height: '1em',
              display: data.type === 'dir' || data.type === 'dirFather' ? 'inline-block' : 'none',
              marginRight: '8px'
            },
            onClick: (event) => {
              event.stopPropagation()
              folderAppend(data)
            }
          }),

          h(resolveComponent('Edit'), {
            ...buttonProps,
            icon: 'Edit',
            style: {
              width: '1em',
              height: '1em',
              display:
                data.type === 'nodata' || data.type === 'dirFather' ? 'none' : 'inline-block',
              marginRight: '8px'
            },
            onClick: (event) => {
              event.stopPropagation()
              amendName(root, node, data)
            }
          }),
          h(resolveComponent('Delete'), {
            ...buttonProps,
            icon: 'Delete',
            style: {
              width: '1em',
              height: '1em',
              display:
                data.type === 'nodata' || data.type === 'dirFather' ? 'none' : 'inline-block',
              marginRight: '8px'
            },
            onClick: (event) => {
              event.stopPropagation()
              remove(root, node, data)
            }
          })
        ]
      )
    ]
  )
}
</script>

<style scoped lang="scss">
.tree_layout {
  width: 100%;

  :has(> :not(.is-empty) > .is-empty) > :first-child {
    display: none;
  }
  padding-right: 5px;
}
:deep(.demo-tree-render .ivu-tree-title) {
  width: calc(100% - 25px);
}
/* 叶子节点隐藏箭头 */
.demo-tree-render :deep(.tree-leaf .ivu-tree-arrow) {
  display: none !important;
}

/* 目录节点显示箭头 */
.demo-tree-render :deep(.tree-dir .ivu-tree-arrow) {
  display: inline-block;
}
</style>
