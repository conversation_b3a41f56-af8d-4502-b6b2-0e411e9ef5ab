import { createApp } from 'vue'
import { HtmlNode, HtmlNodeModel } from '@logicflow/core'
import nodeModule from './databaseNode.vue'

const registerNode = (lf) => {
  class databaseNode extends HtmlNode {
    setHtml(rootEl) {
      const { model } = this.props
      const el = document.createElement('div')
      rootEl.innerHTML = ''
      rootEl.appendChild(el)
      createApp(nodeModule, {
        properties: model.properties
      }).mount(el)
    }
  }
  class databaseNodeModel extends HtmlNodeModel {
    setAttributes() {
      const width = 200
      const height = 100
      this.width = width
      this.height = height
      this.anchorsOffset = [
        [width / 2, 0],
        [-width / 2, 0]
      ]
    }
  }
  lf.register({
    type: 'database-node',
    view: databaseNode,
    model: databaseNodeModel
  })
}
export default registerNode
