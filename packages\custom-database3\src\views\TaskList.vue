<template>
  <div class="task_box">
    <div class="task_header">
      <el-button 
        :disabled="!taskSelected.length"
        type="danger"
        size="small"
        style="margin-right:20px"
        @click="batchDeleteTask"
      >
        删除
      </el-button>
      <span>任务状态：</span>
      <el-radio-group 
        v-model="queryTaskStore.taskTypeRadio"
        @change="taskTypeChange"
      >
        <el-radio value="all">全部</el-radio>
        <el-radio value="unfinished">进行中</el-radio>
        <el-radio value="pause">暂停中</el-radio>
        <el-radio value="error">失败</el-radio>
      </el-radio-group>
    </div>
    <div class="task_body">
      <div class="table">
        <el-table
          ref="multipleTableRef"
          :data="queryTaskStore.pageList"
          row-key="row"
          style="width: 100%"
          tooltip-effect="light myTooltip"
          @selection-change="tableSelectionChange"
        >
          <el-table-column 
            v-if="queryTaskStore.taskTypeRadio!='unfinished'"
            type="selection" 
            width="55" 
          />
          <el-table-column
            v-if="queryTaskStore.taskTypeRadio=='unfinished'"
            width="55"
          >
            <template #header>
              <el-icon class="is-loading" style="font-size: 20px">
                <Loading />
              </el-icon>
            </template>
          </el-table-column>
          <el-table-column
            prop="columnValues.info.title"
            label="任务名" 
            show-overflow-tooltip
            width="200"
          >
          </el-table-column>
          <el-table-column
            prop="columnValues.parm.query_string"
            label="查询字符串" 
            show-overflow-tooltip
            width="400"
          >
          </el-table-column>
          <el-table-column
            prop="columnValues.parm.data.dataTypeTitle"
            label="数据类型" 
            show-overflow-tooltip
            width="400"
          >
          </el-table-column>
          <el-table-column
            label="任务状态" 
            width="120"
          >
            <template #default="scope">
              <span
                v-if="scope.row.columnValues.info.status=='error'"
                style="color: #f56c6c; font-weight: bold"
              >
                {{enTozh(scope.row.columnValues.info.status)}}
              </span>
              <span
                v-else
              >
                {{enTozh(scope.row.columnValues.info.status)}}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间" 
            width="120"
          >
            <template #default="scope">
              <span>
                {{$tools.timestamp(parseInt(scope.row.columnValues.parm.create_timestamp))}}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="right">
            <template #default="scope">
              <el-button 
                v-if="scope.row.columnValues.info.status=='parse_end'"
                size="small" 
                @click="downloadFile(scope.$index, scope.row)"
              >
                下载
              </el-button>
              <el-button 
                v-if="scope.row.columnValues.info.status=='parse_end' ||
                  scope.row.columnValues.info.status=='error'
                "
                size="small" 
                type="primary"
                @click="restart(scope.$index, scope.row)"
              >
                重新分析
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deleteTask(scope.$index, scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label=" " width="50">
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination 
          layout="pager"
          hide-on-single-page
          :page-size="20"
          :total="queryTaskStore.taskNum" 
          @current-change="pageChange"
        />
      </div>
      
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, inject, onUnmounted} from 'vue'
import { Message } from 'view-ui-plus'
import { ElMessageBox } from 'element-plus'
import { useQueryTaskStore } from '@/stores/queryTask'
const $tools = inject('$tools')
import enTozh from '@/utils/zhMap'
const queryTaskStore = useQueryTaskStore()

onMounted(() => {
  queryTaskStore.taskTypeRadio='all'
  // queryTaskStore.getTaskNum()
  queryTaskStore.clearTaskList()
  queryTaskStore.getTaskList()
})

onUnmounted(()=>{
  if (test){
    test()
  }
  if (loopGetTaskListInterval){
    clearInterval(loopGetTaskListInterval)
    loopGetTaskListInterval = null
  }
})

// const taskTypeRadio = ref('all')
let test = null
const taskTypeChange = ()=>{
  console.log("<taskTypeChange> taskTypeRadio:", queryTaskStore.taskTypeRadio);
  multipleTableRef.value.clearSelection()
  queryTaskStore.clearTaskList()

  if (queryTaskStore.taskTypeRadio == 'unfinished'){
    Message.destroy()
    test = Message.loading({
      background: true,
      content: '自动刷新已开启',
      duration: 0
    })
    loopGetTaskList()
  }else {
    if (test){
      test()
    }
    if (loopGetTaskListInterval){
      clearInterval(loopGetTaskListInterval)
      loopGetTaskListInterval = null
    }
    setTimeout(()=>{
      queryTaskStore.getTaskList()
    }, 500)
  }
}
const taskSelected = ref([])
const multipleTableRef = ref()
const tableSelectionChange = (val)=>{
  console.log("<tableSelectionChange> val:", val);

  taskSelected.value = val
  console.log("<tableSelectionChange> taskSelected:", taskSelected.value);
}

const downloadFile = (index, row)=>{
  console.log("<downloadFile> index:", index);
  console.log("<downloadFile> row:", row);

  let fileName = $tools.md5(row.columnValues.info.title) + row.columnValues.parm.create_timestamp

  console.log("<downloadFile> fileName:", fileName);
  const downloadUrl = `/filesystem/api/rest/v2/node-0/main_file/get/public/key_person_excel_files/${fileName}`;
  fetch(downloadUrl)
  .then((response) => {
    if (response.status === 200) {
      return response.blob();
    } else {
      Message.error({
        background: true,
        content: "下载文件失败！",
      });
    }
  })
  .then((blob) => {
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = row.columnValues.info.title + '_' + 
      $tools.timestamp(parseInt(row.columnValues.parm.create_timestamp)) +
      '.xlsx';
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
  })
  .catch((err) => {
    Message.error({
      background: true,
      content: err,
    });
  });
}

const restart = (index, row)=> {
  console.log("<restart> index:", index);
  console.log("<restart> row:", row);
  
  queryTaskStore.restart(row.row)
  queryTaskStore.clearTaskList()
  setTimeout(()=>{
    queryTaskStore.getTaskList()
  }, 100)
}

const deleteTask = (index, row)=>{
  console.log("<deleteTask> index:", index);
  console.log("<deleteTask> row:", row);

  ElMessageBox.confirm(
    '此操作将永久删除已选择的任务, 是否继续?',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(() => {
    queryTaskStore.deleteTask(row.row)
    if (row.columnValues.info.status=='parse_end'){
      queryTaskStore.deleteTaskFile([$tools.md5(row.columnValues.info.title) + 
        row.columnValues.parm.create_timestamp
      ])
    }
  })
  .catch(() => {
    Message.info({
      background: true,
      content: '已取消删除'
    })
  })
}

const batchDeleteTask = ()=>{
  console.log("<batchDeleteTask> taskSelected:", taskSelected.value);

  ElMessageBox.confirm(
    '此操作将永久删除已选择的任务, 是否继续?',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(() => {
    let tmpRows = []
    let tmpFilePath = []
    taskSelected.value.forEach(task=>{
      tmpRows.push(task.row)
      if (task.columnValues.info.status=='parse_end'){
        tmpFilePath.push($tools.md5(task.columnValues.info.title) + 
          task.columnValues.parm.create_timestamp
        )
      }
    })
    console.log("<batchDeleteTask> tmpRows:", tmpRows);
    console.log("<batchDeleteTask> tmpFilePath:", tmpFilePath);

    queryTaskStore.deleteTask(tmpRows)
    queryTaskStore.deleteTaskFile(tmpFilePath)
  })
  .catch(() => {
    Message.info({
      background: true,
      content: '已取消删除'
    })
  })
}

let loopGetTaskListInterval = null
const loopGetTaskList = ()=>{
  loopGetTaskListInterval = setInterval(() => {
    queryTaskStore.getTaskList()
  }, 1000)
}

const pageChange = (page)=> {
  console.log("<pageChange> page:", page);
  
  queryTaskStore.pageList = this.taskList.splice((page - 1) * 20, 20)
}
</script>

<style lang="scss" scoped>
.task_box{
  height: 98.5%;
  width: 99.6%;
  border-radius: 5px;
  margin-top: 6px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  .task_header{
    height: 6%;
    margin-left: 10px;
    display: flex;
    align-items: center;
    span{
      font-size: 16px;
      font-weight: bold;
    }
  }
  .task_body{
    height: 94%;
    .table{
      height: 95%;
      
    }
    .pagination{
      display: flex;
      justify-content: center;
    }

  }
}
</style>
<style lang="scss">
.myTooltip{
  max-width: 50% !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  padding: 8px 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}
</style>
