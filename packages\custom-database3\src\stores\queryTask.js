import { defineStore } from 'pinia'
import { useLoginStore } from './login'
import { Message } from 'view-ui-plus'

export const useQueryTaskStore = defineStore('queryTaskStore', {
  state: () => ({
		taskTypeRadio: 'all',
    taskList: [],
		pageList: [],
		taskNum: 0,
		lastRow: [],
		taskStatus: []
  }),
  actions: {
		getTaskNum(){
			let tmpStatus = []
			switch (this.taskTypeRadio) {
				case 'all':
					tmpStatus = []
					break;
				case 'unfinished':
					tmpStatus = ['initializing', 'parsing']
					break;
				case 'pause':
					tmpStatus = ['parse_ready', 'parse_pause']
					break;
				case 'error':
					tmpStatus = ['error']
					break;
				default:
					return
			}

			const { $data } = useLoginStore().wsMethod
			$data.sendData(
        'Api.DataAnalysisTask.Count',
        [
          {
            head: {
            },
            msg:{
							task_authority: "username",
							task_type:'search_task',
							familys: ["info", "parm"],
							status: tmpStatus
						}
          }
        ],
        (res) => {
          console.log("<getTaskNum> res:", res);
					this.taskNum = res
        }
      )
		},

		clearTaskList () {
			this.taskList = []
			this.pageList = []
			this.lastRow = []
			this.taskNum = 0

			switch (this.taskTypeRadio) {
				case 'all':
					this.taskStatus = []
					break;
				case 'unfinished':
					this.taskStatus = ['initializing', 'parsing']
					break;
				case 'pause':
					this.taskStatus = ['parse_ready', 'parse_pause']
					break;
				case 'error':
					this.taskStatus = ['error']
					break;
				default:
					return
			}
		},

    // 获取任务列表
		getTaskList() {
			console.log("<getTaskList>");
			
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.DataAnalysisTask.List',
        [
          {
            head: {
              size: 20,
							row_key: this.lastRow.length?this.lastRow:[]
            },
            msg:{
							task_authority: "username",
							task_type:'search_task',
							familys: ["info", "parm"],
							status: [...new Set(this.taskStatus)]
						}
          }
        ],
        (res) => {
          console.log("<getTaskList> res:", res);
					
					this.taskList = this.taskList.concat(res)
					if (res.length >= 20){
						console.log("<getTaskList> unfinished.");
						this.lastRow.push(res[res.length-1].row)
						console.log("<getTaskList> lastRow:", this.lastRow);
						this.getTaskList()
					}else {
						console.log("<getTaskList> get all.");
						this.taskNum = this.taskList.length
						this.pageList = this.taskList.splice(0, 20)
						if (this.taskTypeRadio == 'unfinished')
							return
						if (!res.length) {
							Message.info({
								background: true,
								content: '暂无任务'
							})
						} else {
							Message.success({
								background: true,
								content: '任务获取成功'
							})
						}
					}
        }
      )
    },

		restart(row) {
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.DataAnalysisTask.SetStatus',
        [
          {
            head: {
              row_key: [row]
            },
            msg: {
              task_type: 'search_task',
              task_authority: 'username',
              status: 'parse_ready'
            }
          }
        ],
        (res) => {
          if (res?.status === 'ok') {
            Message.success({
              background: true,
              content: '开始重新分析'
            })
          }
        }
      )
    },

		// 删除任务
		deleteTask(v) {
			console.log("<deleteTask> v:", v);

			let tmpRow = []
			if (Array.isArray(v)){
				console.log("<deleteTask> 数组");
				tmpRow = [...v]
			}else if ((typeof v) == "string"){
				console.log("<deleteTask> 字符串");
				tmpRow = [v]
			}else {
				console.log("<deleteTask> 其他类型");
				return
			}

			console.log("<deleteTask> tmpRow:", tmpRow);

      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.DataAnalysisTask.Del',
        [
          {
            head: {
							row_key: tmpRow
            },
            msg:{
							task_authority: "username",
							task_type:'search_task',
						}
          }
        ],
        (res) => {
          console.log("<deleteTask> res:", res);
					if (res?.status === 'ok') {
            Message.success({
              background: true,
              content: '任务删除成功'
            })
            this.getTaskList()
          } else {
            Message.error({
              background: true,
              content: '任务删除失败'
            })
          }
        }
      )
    },

		deleteTaskFile (v){
			console.log("<deleteTaskFile> v:", v);

			const { $file } = useLoginStore().wsMethod
			v.forEach((fileName)=>{
				$file.sendData(
					"Api.MainFile.Rm",
					[
						{
							head: {},
							msg: {
								file_path: "key_person_excel_files",
								file_name: fileName,
								file_type: 'public',
							},
						},
					],
					(res) => {
						console.log("<deleteTaskFile> res:", res);
					}
				)
			})
		}
  }
})
