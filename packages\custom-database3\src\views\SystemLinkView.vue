<template>
  <div class="model_lay">
    <div>
      <div class="title">内网子系统</div>
      <div class="content_row">
        <a :href="root_main" class="icon">
          <img src="@/assets/image/home.png" />
          <p>返回主系统</p>
        </a>
        <a :href="root_backstage" class="icon">
          <img src="@/assets/image/logoridus.png" />
          <p>后台管理系统</p>
        </a>
      </div>
    </div>
    <div>
      <div class="title">功能性子系统</div>
      <div class="content_row">
        <a :href="root_twitter_analysis" class="icon">
          <img src="@/assets/image/twitter.png" />
          <p>twitter分析系统</p>
        </a>
        <a :href="root_doc" class="icon">
          <img src="@/assets/image/document.png" />
          <p>文档邮件分析系统</p>
        </a>
        <a :href="root_self" class="icon">
          <img src="@/assets/image/zdy_logo.png" />
          <p>自定义库管理系统</p>
        </a>
        <a :href="root_custom" class="icon">
          <img src="@/assets/image/custom.jpg" />
          <p>智网社情分析系统</p>
        </a>
      </div>
    </div>
    <div>
      <div class="title">社工库子系统</div>
      <div class="content_row">
        <a :href="root_sina" class="icon">
          <img src="@/assets/image/sina.png" />
          <p>微博查询系统</p>
        </a>
        <a :href="root_facebook" class="icon">
          <img src="@/assets/image/facebook.png" />
          <p>Facebook查询系统</p>
        </a>
        <a :href="root_jingdong" class="icon">
          <img src="@/assets/image/jd.png" />
          <p>京东查询系统</p>
        </a>
        <a :href="root_macao" class="icon">
          <img src="@/assets/image/macao.png" />
          <p>澳门通关信息<br />查询系统</p>
        </a>
        <a :href="root_wangyi" class="icon">
          <img src="@/assets/image/wangyi.png" />
          <p>网易信息查询系统</p>
        </a>
        <a :href="root_tencent" class="icon">
          <img src="@/assets/image/tencent.png" />
          <p>腾讯信息查询系统</p>
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
const root_main = ref('')
const root_backstage = ref('')
const root_twitter_analysis = ref('')
const root_doc = ref('')
const root_self = ref('')
const root_custom = ref('')
const root_sina = ref('')
const root_facebook = ref('')
const root_jingdong = ref('')
const root_macao = ref('')
const root_wangyi = ref('')
const root_tencent = ref('')

onMounted(() => {
  const host = window.location.host.split(':')
  root_main.value = `https://${host[0]}:8800`
  root_backstage.value = `https://${host[0]}:8880`
  root_twitter_analysis.value = `https://${host[0]}:8904`
  root_doc.value = `https://${host[0]}:8903`
  root_self.value = `https://${host[0]}:8910`
  root_custom.value = `https://${host[0]}:8918`
  root_sina.value = `https://${host[0]}:8901`
  root_facebook.value = `https://${host[0]}:8902`
  root_jingdong.value = `https://${host[0]}:8905`
  root_macao.value = `https://${host[0]}:8909`
  root_wangyi.value = `https://${host[0]}:9005`
  root_tencent.value = `https://${host[0]}:9008`
})
</script>

<style lang="scss" scoped>
.model_lay {
  background: #fff;
  margin-top: 10px;
}
.icon {
  display: block;
  text-align: center;
  flex-direction: column;
  margin-left: 20px;
  margin-bottom: 20px;
}
.title {
  border-top: 3px solid #eee;
  border-bottom: 1px solid #eee;
  padding-left: 25px;
  color: #606266;
  font-size: 20px;
  height: 40px;
  line-height: 40px;
}
.content_row {
  margin-top: 10px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
</style>
