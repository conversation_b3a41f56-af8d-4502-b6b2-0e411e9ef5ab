/* 修改滚动条的宽度、颜色和圆角 */
::-webkit-scrollbar {
    width: 7px; /* 宽度 */
    height: 7px; /* 高度（垂直滚动条） */
  }
  
  /* 滚动条轨道 */
  ::-webkit-scrollbar-track {
    background-color: #f1f1f1; /* 轨道背景色 */
    border-radius: 5px; /* 圆角 */
  }
  
  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    background-color: #888; /* 滑块颜色 */
    border-radius: 5px; /* 圆角 */
  }
  
  /* 滚动条滑块悬停状态 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #555; /* 滑块悬停时颜色 */
  }
  
  /* 滚动条按钮（上下箭头） */
  ::-webkit-scrollbar-button {
    display: none; /* 隐藏按钮 */
  }
  